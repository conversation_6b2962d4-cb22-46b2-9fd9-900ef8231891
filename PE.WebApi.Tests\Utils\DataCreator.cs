﻿#pragma warning disable CS9124

namespace PE.WebApi.Tests.Utils
{
    internal class DataCreator<TDto>(HttpClient client, string url)
        where TDto : class
    {
        protected HttpClient Client { get; set; } = client;
        public string Url { get; set; } = url;


        public virtual async Task<TDto> CreateAsync(TDto dto)
        {
            var rst = await PostAndReadResultFromJsonAsync(dto);

            rst.Should().NotBeNull();

            return rst!;
        }


        protected async Task<TDto?> PostAndReadResultFromJsonAsync(TDto dto)
        {
            var resp = await client.PostAsJsonAsync(url, dto);

            resp.StatusCode.Should().Be(HttpStatusCode.OK);

            return await resp.Content.ReadFromJsonAsync<TDto>();
        }
    }


    internal class DataCreator<TReq, TRst>(HttpClient client, string url)
        where TReq : class
        where TRst : class
    {
        protected HttpClient Client { get; set; } = client;
        public string Url { get; set; } = url;


        public virtual async Task<TRst> CreateAsync(TReq dto)
        {
            var rst = await PostAndReadResultFromJsonAsync(dto);

            rst.Should().NotBeNull();

            return rst!;
        }


        protected async Task<TRst?> PostAndReadResultFromJsonAsync(TReq dto)
        {
            var resp = await client.PostAsJsonAsync(url, dto);

            resp.StatusCode.Should().Be(HttpStatusCode.OK);

            return await resp.Content.ReadFromJsonAsync<TRst>();
        }
    }
}
