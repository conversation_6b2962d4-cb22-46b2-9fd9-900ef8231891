﻿using System.Linq.Expressions;

namespace PE.Domain.Base
{
    public interface IBaseRepository<T> : IRepository<T>
        where T : Entity, IAggregateRoot, new()
    {
        //Task IncludeAsync(T entity);

        Task AddAsync(T entity);

        Task<int> AddReturnIdentityAsync(T entity);

        Task<long> AddReturnBigIdentityAsync(T entity);

        Task AddRangeAsync(List<T> entities);

        Task UpdateAsync(T entity);

        Task DeleteAsync(object id);

        Task<T> GetSingleAsync(Expression<Func<T, bool>>? expression = null);

        //Task<List<T>> GetListAsync(Expression<Func<T, bool>>? expression = null);
        Task<List<T>> GetListAsync();

        //Task<(List<T> list, int totalNumber)> GetPageAsync(int pageIndex, int pageSize, Expression<Func<T, bool>>? expression = null);

        Task<(List<T> list, int totalNumber)> GetPageAsync(int pageIndex, int pageSize);
    }
}
