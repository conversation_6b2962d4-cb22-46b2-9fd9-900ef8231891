﻿using PE.Domain.FollowUpMng.Relations;

namespace PE.Domain.FollowUpMng.Entities;

[SugarTable("follow_up_plan", TableDescription = "随访计划")]
public class FollowUpPlan : Entity, IAggregateRoot
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }


    public FollowUpPlanStatus Status { get; set; }



    [SugarColumn(IsIgnore = true)]
    public IRecordOfFollowUpPlan? Records { get; private set; }


    public override object GetIdentity()
    {
        return Id;
    }


    public void SetRelations(IRecordOfFollowUpPlan records)
    {
        Records = records;
    }
}
