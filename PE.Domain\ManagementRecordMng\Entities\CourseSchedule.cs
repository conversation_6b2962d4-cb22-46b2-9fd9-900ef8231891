﻿using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 日程
/// </summary>
[SugarTable("physio_course_schedule", TableDescription = "理疗日程")]
[SugarIndex("index_{table}_management_course_id", nameof(CourseId), OrderByType.Desc)]
public class CourseSchedule : Entity
{
    /// <summary>
    /// Id
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 疗程Id
    /// </summary>
    [SugarColumn(ColumnName = "course_id", ColumnDescription = "疗程Id")]
    public long CourseId { get; set; }

    /// <summary>
    /// 日程序号
    /// </summary>
    [SugarColumn(ColumnName = "serial_no", ColumnDescription = "日程序号")]
    public int SerialNo { get; set; }

    /// <summary>
    /// 预期日期
    /// </summary>
    [SugarColumn(ColumnName = "expected_date", ColumnDescription = "预期日期")]
    public DateTime? ExpectedDate { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execution_time", ColumnDescription = "执行时间")]
    public DateTime? ExecutionTime { get; set; }

    /// <summary>
    /// 负责人Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_person_id", ColumnDescription = "负责人Id")]
    public long? ChargePersonId { get; set; }

    /// <summary>
    /// 负责人姓名
    /// </summary>
    [SugarColumn(ColumnName = "charge_person_name", ColumnDescription = "负责人姓名")]
    public string? ChargePersonName { get; set; }

    /// <summary>
    /// 是否完成
    /// </summary>
    [SugarColumn(ColumnName = "is_complete", ColumnDescription = "是否完成")]
    public bool IsComplete { get; set; }

    /// <summary>
    /// 日程明细
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IDetailOfSchedule? Details { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
