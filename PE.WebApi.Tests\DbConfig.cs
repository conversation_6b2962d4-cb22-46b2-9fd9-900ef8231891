﻿//#define USE_SQLSERVER

using SqlSugar;

namespace PE.WebApi.Tests
{
    public static class DbConfig
    {
        private static string _connectionString = "Host=localhost;Database=HealthTest;Username=********;Password=********;";
        private static DbType _dbType = DbType.PostgreSQL;

        public static string GetConnectionString()
        {
            return _connectionString;
        }

        public static void SetConnectionString(string connectionString)
        {
            _connectionString = connectionString;
        }

        public static DbType GetDbType()
        {
            return _dbType;
        }

        public static void SetDbType(DbType dbType)
        {
            _dbType = dbType;
        }
    }
}
