﻿using System.Linq.Expressions;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Infrastructure.ManagementRecordMng.Relations;

public class QuestionnaireOfManagementRecord(HealthDbContext dbContext, ManagementRecord record)
    : BaseRelation<ManagementRecord, PersonalInformationQuestionnaire>(dbContext, record), IQuestionnaireOfManagementRecord
{
    public override async Task<PersonalInformationQuestionnaire> GetSingleAsync(Expression<Func<PersonalInformationQuestionnaire, bool>>? expression = null)
    {
        // 构建查询条件，确保只查询与当前管理记录相关的问卷
        var query = DbContext.Db.Queryable<PersonalInformationQuestionnaire>()
            .Where(q => q.ManagementRecordId == Master.Id);

        // 如果有额外的查询条件，添加到查询中
        if (expression != null)
        {
            query = query.Where(expression);
        }

        // 获取问卷信息
        return await query.FirstAsync();
    }

    //public override async Task<List<PersonalInformationQuestionnaire>> GetListAsync(Expression<Func<PersonalInformationQuestionnaire, bool>>? expression = null)
    //{
    //    // 构建查询条件，确保只查询与当前管理记录相关的问卷
    //    var query = DbContext.Db.Queryable<PersonalInformationQuestionnaire>()
    //        .Where(q => q.ManagementRecordId == Master.Id);

    //    // 如果有额外的查询条件，添加到查询中
    //    if (expression != null)
    //    {
    //        query = query.Where(expression);
    //    }

    //    // 获取问卷列表
    //    return await query.ToListAsync();
    //}
}
