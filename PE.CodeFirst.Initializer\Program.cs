﻿namespace PE.CodeFirst.Initializer
{
    internal class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("DB initialization begins.");
                Console.WriteLine("========================================");

                Console.WriteLine("Initializing DB...");
                DbInitializer initializer = new(DbConfig.ConnectionString, DbConfig.DbType);
                initializer.Init();
                Console.WriteLine("DB initialization completed.");
                Console.WriteLine("----------------------------------------");

                Console.WriteLine("Initializing seed-data...");
                SeedDataInitializer dataInitializer = new(DbConfig.ConnectionString, DbConfig.DbType);
                dataInitializer.Init();
                Console.WriteLine("Seed-data initialization completed.");
                Console.WriteLine("----------------------------------------");

                Console.WriteLine("========================================");
                Console.WriteLine("DB initialization completed.");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"!!! ERROR: {ex.Message}");
                Console.WriteLine("DB initialization failed.");
            }

            Console.ReadLine();
        }
    }
}
