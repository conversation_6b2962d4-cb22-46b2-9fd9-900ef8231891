﻿//#define USE_SQLSERVER

using PE.Domain.SystemMng.Relations;

namespace PE.Domain.DoctorMng.Entities;

/// <summary>
/// 医生
/// </summary>
[SugarTable(TableName = "sys_doctors", TableDescription = "医生")]
[SugarIndex("unique_{table}_user_name", nameof(UserName), OrderByType.Asc, true)]

public class Doctor : Entity, IAggregateRoot
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(ColumnName = "user_name", ColumnDescription = "用户名")]
    public string? UserName { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [SugarColumn(ColumnName = "password", ColumnDescription = "密码")]
    public string? Password { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "姓名")]
    public string? Name { get; set; }

    /// <summary>
    /// 角色
    /// </summary>
    [SugarColumn(ColumnName = "role_id", ColumnDescription = "角色Id")]
    public int? RoleId { get; set; }

    /// <summary>
    /// 介绍
    /// </summary>
#if USE_SQLSERVER
    [SugarColumn(ColumnName = "introduction", ColumnDataType = "ntext", ColumnDescription = "介绍")]
#else
    [SugarColumn(ColumnName = "introduction", ColumnDataType = "text", ColumnDescription = "介绍")]
#endif
    public string? Introduction { get; set; }

    /// <summary>
    /// 角色
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IRoleOfUser? Role { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
