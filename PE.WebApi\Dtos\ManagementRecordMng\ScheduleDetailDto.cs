﻿//#pragma warning disable CS8618

namespace PE.WebApi.Dtos.ManagementRecordMng;

public class ScheduleDetailDto
{
    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 疗程ID
    /// </summary>
    public long CourseId { get; set; }

    /// <summary>
    /// 日程ID
    /// </summary>
    public long ScheduleId { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    public string? ItemRemark { get; set; }

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsComplete { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? BegingTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行备注
    /// </summary>
    public string? ExecRemark { get; set; }

    /// <summary>
    /// 理疗师Id
    /// </summary>
    public long? PhysioId { get; set; }

    /// <summary>
    /// 理疗师姓名
    /// </summary>
    public string? PhysioName { get; set; }
}
