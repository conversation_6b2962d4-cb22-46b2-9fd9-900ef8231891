﻿﻿using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Dtos.ManagementRecordMng;

namespace PE.WebApi.AutoMapperProfiles.ManagementRecordMng
{
    public class AppointmentProfile : Profile
    {
        public AppointmentProfile()
        {
            CreateMap<Appointment, AppointmentDto>().ReverseMap();
            CreateMap<Page4AppointmentDto, AppointmentPageQuery>();

            // 添加 PreBookDto 到 Appointment 的映射
            CreateMap<PreBookDto, Appointment>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => AppointmentStatus.Booked)) // 默认状态为已预约
                .ForMember(dest => dest.CreatorId, opt => opt.MapFrom(src => src.AssistantId))
                .ForMember(dest => dest.CreatorName, opt => opt.MapFrom(src => src.AssistantName))
                .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}
