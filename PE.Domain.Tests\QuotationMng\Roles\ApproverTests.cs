using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;
using PE.Domain.QuotationMng.Roles;
using Moq;

namespace PE.Domain.Tests.QuotationMng.Roles;

public class ApproverTests
{
    private readonly Mock<IQuotationOfApprover> _mockQuotations = new();
    private readonly Doctor _doctor = new() { Id = 2, Name = "李四" };

    private Approver CreateApprover() => new(_doctor, _mockQuotations.Object);

    [Fact]
    public async Task ApproveQuotationAsync_ShouldCallApproveMethod()
    {
        // Arrange
        const long quotationId = 1;

        // Act
        var approver = CreateApprover();
        await approver.ApproveQuotationAsync(quotationId);

        // Assert
        _mockQuotations.Verify(q => q.ApproveQuotationAsync(quotationId), Times.Once);
    }

    [Fact]
    public async Task GetPendingQuotationsAsync_ShouldReturnPendingQuotationsList()
    {
        // Arrange
        var expectedQuotations = new List<Quotation>
        {
            new() { Id = 1, Status = QuotationStatus.PendingApproval },
            new() { Id = 2, Status = QuotationStatus.PendingApproval }
        };

        _mockQuotations.Setup(q => q.GetPendingQuotationsAsync())
            .ReturnsAsync(expectedQuotations);

        // Act
        var approver = CreateApprover();
        var result = await approver.GetPendingQuotationsAsync();

        // Assert
        Assert.Equal(expectedQuotations, result);
        _mockQuotations.Verify(q => q.GetPendingQuotationsAsync(), Times.Once);
    }

    [Fact]
    public async Task GetApprovedQuotationsAsync_ShouldReturnApprovedQuotationsList()
    {
        // Arrange
        var expectedQuotations = new List<Quotation>
        {
            new() { Id = 1, Status = QuotationStatus.Approved, AuditorId = (int)_doctor.Id },
            new() { Id = 2, Status = QuotationStatus.Approved, AuditorId = (int)_doctor.Id }
        };

        _mockQuotations.Setup(q => q.GetApprovedQuotationsAsync())
            .ReturnsAsync(expectedQuotations);

        // Act
        var approver = CreateApprover();
        var result = await approver.GetApprovedQuotationsAsync();

        // Assert
        Assert.Equal(expectedQuotations, result);
        _mockQuotations.Verify(q => q.GetApprovedQuotationsAsync(), Times.Once);
    }

    [Fact]
    public async Task RejectQuotationAsync_ShouldUpdateStatusToDraft_WhenPendingApproval()
    {
        // Arrange
        const long quotationId = 1;
        const string reason = "价格过高";
        var quotation = new Quotation
        {
            Id = quotationId,
            Status = QuotationStatus.PendingApproval
        };

        _mockQuotations.Setup(q => q.GetSingleAsync(It.IsAny<Expression<Func<Quotation, bool>>>()))
            .ReturnsAsync(quotation);

        // Act
        var approver = CreateApprover();
        await approver.RejectQuotationAsync(quotationId, reason);

        // Assert
        Assert.Equal(QuotationStatus.Draft, quotation.Status);
        Assert.Equal(reason, quotation.Remark);
        _mockQuotations.Verify(q => q.UpdateAsync(quotation), Times.Once);
    }

    [Fact]
    public async Task RejectQuotationAsync_ShouldNotUpdateStatus_WhenNotPendingApproval()
    {
        // Arrange
        const long quotationId = 1;
        const string reason = "价格过高";
        var quotation = new Quotation
        {
            Id = quotationId,
            Status = QuotationStatus.Approved
        };

        _mockQuotations.Setup(q => q.GetSingleAsync(It.IsAny<Expression<Func<Quotation, bool>>>()))
            .ReturnsAsync(quotation);

        // Act
        var approver = CreateApprover();
        await approver.RejectQuotationAsync(quotationId, reason);

        // Assert
        Assert.Equal(QuotationStatus.Approved, quotation.Status);
        _mockQuotations.Verify(q => q.UpdateAsync(It.IsAny<Quotation>()), Times.Never);
    }
}
