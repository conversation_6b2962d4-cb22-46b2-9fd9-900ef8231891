﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;

namespace PE.Infrastructure.Core;

public class UnitOfWorkFilter(ISqlSugarClient db) : IActionFilter
{
    readonly ISqlSugarClient _db = db;


    public void OnActionExecuting(ActionExecutingContext context)
    {
        _db.Ado.BeginTran();
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        if (context.Exception == null)
        {
            _db.Ado.CommitTran();
        }
        else
        {
            _db.Ado.RollbackTran();
        }
    }
}


[AttributeUsage(AttributeTargets.Method)]
public class UnitOfWorkAttribute : Attribute, IAsyncActionFilter, IAsyncExceptionFilter
{
    //private ISqlSugarClient db;

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var db = context.HttpContext.RequestServices.GetRequiredService<ISqlSugarClient>();

        try
        {
            db.Ado.BeginTran();

            var resultContext = await next();

            if (resultContext.Exception == null || resultContext.ExceptionHandled)
            {
                db.Ado.CommitTran();
            }
            else
            {
                db.Ado.RollbackTran();
            }
        }
        catch (Exception)
        {
            db.Ado.RollbackTran();

            throw;
        }
    }

    public async Task OnExceptionAsync(ExceptionContext context)
    {
        var db = context.HttpContext.RequestServices.GetRequiredService<ISqlSugarClient>();
        db.Ado.RollbackTran();

        await Task.CompletedTask;
    }
}
