﻿namespace PE.Domain.ManagementRecordMng.Entities;

[SugarTable("ques_personal_information_questionnaire", TableDescription = "个人问卷")]
[SugarIndex("index_{table}_management_record_id", nameof(ManagementRecordId), OrderByType.Desc)]
public class PersonalInformationQuestionnaire : Entity
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    [SugarColumn(ColumnName = "management_record_id", ColumnDescription = "管理记录Id")]
    public long ManagementRecordId { get; set; }

    [SugarColumn(ColumnName = "questionnaire", ColumnDataType = "text", ColumnDescription = "问卷")]
    public string? Questionnaire { get; set; }

    [SugarColumn(ColumnName = "answer", ColumnDataType = "text", ColumnDescription = "答题卡")]
    public string? Answer { get; set; }

    [SugarColumn(ColumnName = "version", ColumnDescription = "版本号")]
    public int Version { get; set; }

    public override object GetIdentity()
    {
        return Id;
    }
}
