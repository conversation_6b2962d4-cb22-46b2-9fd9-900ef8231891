﻿using PE.Domain.Abstractions;
using PE.Domain.CustomerMng.Entities;
using SqlSugar;
using System.Reflection;

namespace PE.CodeFirst.Initializer
{
    public class DbInitializer(string connecetionString, DbType dbType,
        int stringDefaultLength = 50, bool hasDb = false)
    {
        public string ConnecetionString { get; } = connecetionString;

        public DbType DbType { get; } = dbType;

        public int StringDefaultLength { get; } = stringDefaultLength;

        public bool HasDb { get; } = hasDb;


        public void Init()
        {
            try
            {
                var db = BuildDbClient();

                if (!HasDb)
                {
                    db.DbMaintenance.CreateDatabase();
                }

                InitTables(db);
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("exist"))
                {
                    
                }
                else
                {
                    throw;
                }
            }
        }


        private SqlSugarClient BuildDbClient()
        {
            DbClientBuilder clientBuilder = new(ConnecetionString, DbType);
            clientBuilder.SetConfigureExternalServices(new ConfigureExternalServices()
            {
                EntityService = (c, p) =>
                {
                    if (!p.IsPrimarykey && new NullabilityInfoContext().Create(c).WriteState is NullabilityState.Nullable)
                    {
                        p.IsNullable = true;
                    }
                }
            });

            return clientBuilder.Build();
        }

        private void InitTables(SqlSugarClient db)
        {
            var types = typeof(Customer).Assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Entity)))
                .ToArray();

            db.CodeFirst
                .SetStringDefaultLength(StringDefaultLength)
                .InitTables(types);
        }
    }
}
