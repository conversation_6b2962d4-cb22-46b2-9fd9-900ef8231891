﻿namespace PE.WebApi.Tests.Utils
{
    internal static class Urls
    {
        private const string Root = "api";

        #region 用户

        internal static class Customer
        {
            private const string Controller = $"{Root}/Customer";

            public const string Create = $"{Controller}/create";
            public const string Get = $"{Controller}";
            public const string GetPage = $"{Controller}/page";
            public const string Update = $"{Controller}/update";
            //public const string ImportByExcel = $"{Controller}/importByExcel";
        }


        internal static class User
        {
            private const string Controller = $"{Root}/user";

            public const string AdminLogin = $"{Controller}/admin/login";
            public const string DoctorLogin = $"{Controller}/doctor/login";
        }


        internal static class Doctor
        {
            private const string Controller = $"{Root}/doctor";

            public const string Create = $"{Controller}/create";
            public const string Update = $"{Controller}/update";
            public const string List = $"{Controller}/list";
            public const string Get = $"{Controller}/{{0}}";
        }


        internal static class Role
        {
            private const string Controller = $"{Root}/role";

            public const string Create = $"{Controller}/create";
            public const string Update = $"{Controller}/update";
            public const string List = $"{Controller}/list";
        }

        #endregion


        #region 管理记录

        internal static class Order
        {
            private const string Controller = $"{Root}/order";

            public const string GenerateOrderNo = $"{Controller}/generateOrderNo";
            public const string Place = $"{Controller}/place";
        }

        internal static class ManagementRecord
        {
            private const string Controller = $"{Root}/managementRecord";

            public const string CreateFromDoctor = $"{Controller}/createFromDoctor";
            public const string AssignDoctor = $"{Controller}/assignDoctor";
            public const string List = $"{Controller}/list";
            public const string ListForNew = $"{Controller}/listForNew";
        }

        #endregion


        #region 慢病

        internal static class ChronicDisease
        {
            private const string Controller = $"{Root}/chronicDisease";

            public const string Create = $"{Controller}/create";
            public const string Update = $"{Controller}/update";
            public const string List = $"{Controller}/list";
        }

        internal static class ManagementPackage
        {
            private const string Controller = $"{Root}/managementPackage";

            public const string Create = $"{Controller}/create";
            public const string Update = $"{Controller}/update";
            public const string List = $"{Controller}/list";
            public const string ListForDisease = $"{Controller}/listForDisease";

            internal static class Specification
            {
                public const string Create = $"{Controller}/specification/create";
                public const string Update = $"{Controller}/specification/update";
                public const string List = $"{Controller}/{{0}}/specification/list";
            }
        }



        #endregion
    }
}
