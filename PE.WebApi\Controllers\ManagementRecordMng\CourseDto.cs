﻿namespace PE.WebApi.Controllers.ManagementRecordMng
{
    public class CourseDto
    {
        public long Id { get; set; }

        /// <summary>
        /// 管理记录Id
        /// </summary>
        public long ManagementRecordId { get; set; }

        /// <summary>
        /// 疗程序号
        /// </summary>
        public int SerialNo { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletionTime { get; set; }

        /// <summary>
        /// 状态（0：新建；1：进行中；2：完成）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 周期(字符串，每日、隔天、住宿)
        /// </summary>
        public string? Cycle { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 客户评价
        /// </summary>
        public string? Reviews { get; set; }
    }
}
