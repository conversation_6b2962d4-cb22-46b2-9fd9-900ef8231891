using PE.Domain.QuotationMng.Entities;
using PE.WebApi.Dtos.QuotationMng;

namespace PE.WebApi.Tests.Fakers.QuotationMng;

internal class CreateQuotationDtoFaker : BaseFaker<CreateQuotationDto>
{
    public CreateQuotationDtoFaker()
    {
        DataFaker = new Faker<CreateQuotationDto>()
            .RuleFor(x => x.CompanyId, f => f.Random.Int(1, 1000))
            .RuleFor(x => x.Remark, f => f.Lorem.Sentence())
            .RuleFor(x => x.Packages, f => new CreateExaminationPackageDtoFaker().Generate(f.Random.Int(1, 3)));
    }
}

internal class CreateExaminationPackageDtoFaker : BaseFaker<CreateExaminationPackageDto>
{
    public CreateExaminationPackageDtoFaker()
    {
        DataFaker = new Faker<CreateExaminationPackageDto>()
            .RuleFor(x => x.Id, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.Name, f => f.Commerce.ProductName())
            .RuleFor(x => x.PackageCode, f => f.Random.AlphaNumeric(8))
            .RuleFor(x => x.OriginalPrice, f => f.Random.Decimal(100, 5000))
            .RuleFor(x => x.DiscountRate, f => f.Random.Decimal(0.5m, 1.0m))
            .RuleFor(x => x.DiscountedPrice, (f, x) => x.OriginalPrice * x.DiscountRate)
            .RuleFor(x => x.Description, f => f.Lorem.Paragraph())
            .RuleFor(x => x.IsActive, f => true)
            .RuleFor(x => x.MinAge, f => f.Random.Int(18, 30))
            .RuleFor(x => x.MaxAge, (f, x) => f.Random.Int(x.MinAge, 80))
            .RuleFor(x => x.GenderType, f => f.PickRandom<GenderType>())
            .RuleFor(x => x.MaritalStatus, f => f.PickRandom<MaritalStatus>())
            .RuleFor(x => x.CombinedItems, f => new CreateExaminationCombinedItemDtoFaker().Generate(f.Random.Int(1, 5)));
    }

    public List<CreateExaminationPackageDto> Generate(int count)
    {
        return DataFaker!.Generate(count);
    }
}

internal class CreateExaminationCombinedItemDtoFaker : BaseFaker<CreateExaminationCombinedItemDto>
{
    public CreateExaminationCombinedItemDtoFaker()
    {
        DataFaker = new Faker<CreateExaminationCombinedItemDto>()
            .RuleFor(x => x.Name, f => f.Commerce.ProductName())
            .RuleFor(x => x.Code, f => f.Random.AlphaNumeric(6))
            .RuleFor(x => x.BasePrice, f => f.Random.Decimal(10, 500))
            .RuleFor(x => x.Category, f => f.PickRandom("内科", "外科", "影像科", "检验科", "妇科"))
            .RuleFor(x => x.Description, f => f.Lorem.Sentence());
    }

    public List<CreateExaminationCombinedItemDto> Generate(int count)
    {
        return DataFaker!.Generate(count);
    }
}
