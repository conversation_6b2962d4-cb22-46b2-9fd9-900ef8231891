﻿using PE.WebApi.Dtos.DoctorMng;

namespace PE.WebApi.Tests.Fakers
{
    internal class CreateDoctorRequestFaker : BaseFaker<CreateDoctorRequest>
    {
        public CreateDoctorRequestFaker()
        {
            DataFaker = new Faker<CreateDoctorRequest>()
                .RuleFor(x => x.Name, faker => faker.Name.FullName())
                .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
                .RuleFor(x => x.Password, faker => faker.Internet.Password())
                .RuleFor(x => x.AutographUrl, faker => faker.Internet.Url())
                .RuleFor(x => x.RoleId, faker => faker.Random.Number(1, 10))
            ;
        }
    }
}
