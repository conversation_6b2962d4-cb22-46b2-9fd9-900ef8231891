﻿using System.Linq.Expressions;

namespace PE.Infrastructure.Base
{
    public abstract class BaseRepository<T>(HealthDbContext dbContext) : IBaseRepository<T>
        where T : Entity, IAggregateRoot, new()
    {
        public HealthDbContext DbContext { get; protected init; } = dbContext;


        public virtual async Task<T> GetSingleAsync(Expression<Func<T, bool>>? expression = null)
        {
            if (expression == null)
                return await DbContext.Db.Queryable<T>().SingleAsync();

            return await DbContext.Db.Queryable<T>().Where(expression).SingleAsync();
        }


        public virtual async Task<List<T>> GetListAsync(Expression<Func<T, bool>>? expression = null)
        {
            if (expression == null)
                return await DbContext.Db.Queryable<T>().ToListAsync();

            return await DbContext.Db.Queryable<T>().Where(expression).ToListAsync();
        }


        public virtual async Task<(List<T> list, int totalNumber)> GetPageAsync(int pageIndex, int pageSize, Expression<Func<T, bool>>? expression = null)
        {
            RefAsync<int> totalNumber = 0;
            var list = await DbContext.Db.Queryable<T>()
                .WhereIF(expression is not null, expression)
                .ToPageListAsync(pageIndex, pageSize, totalNumber);
            return (list, totalNumber);
        }


        public virtual async Task AddAsync(T entity)
        {
            await DbContext.Db.Insertable(entity).ExecuteCommandAsync();
        }


        public virtual async Task<int> AddReturnIdentityAsync(T entity)
        {
            return await DbContext.Db.Insertable(entity).ExecuteReturnIdentityAsync();
        }


        public virtual async Task<long> AddReturnBigIdentityAsync(T entity)
        {
            return await DbContext.Db.Insertable(entity).ExecuteReturnBigIdentityAsync();
        }


        public virtual async Task AddRangeAsync(List<T> entities)
        {
            await DbContext.Db.Insertable(entities).ExecuteCommandAsync();
        }


        public virtual async Task DeleteAsync(object id)
        {
            await DbContext.Db.Deleteable<T>(id).ExecuteCommandAsync();
        }


        public virtual async Task UpdateAsync(T entity)
        {
            await DbContext.Db.Updateable(entity).ExecuteCommandAsync();
        }
    }
}
