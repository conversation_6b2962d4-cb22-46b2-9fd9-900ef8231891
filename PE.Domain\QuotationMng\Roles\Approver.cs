﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;

namespace PE.Domain.QuotationMng.Roles;

/// <summary>
/// 审核员
/// </summary>
/// <param name="doctor">医生实体</param>
/// <param name="quotations">报价单关系</param>
public class Approver(Doctor doctor, IQuotationOfApprover quotations)
{
    public Doctor Doctor { get; } = doctor;
    public IQuotationOfApprover Quotations { get; } = quotations;


    /// <summary>
    /// 审核团检报价单
    /// </summary>
    /// <param name="quotationId">报价单ID</param>
    /// <returns></returns>
    public async Task ApproveQuotationAsync(long quotationId)
    {
        await Quotations.ApproveQuotationAsync(quotationId);
    }

    /// <summary>
    /// 获取待审核的报价单列表
    /// </summary>
    /// <returns>待审核报价单列表</returns>
    public async Task<List<Quotation>> GetPendingQuotationsAsync()
    {
        return await Quotations.GetPendingQuotationsAsync();
    }

    /// <summary>
    /// 获取已审核的报价单列表
    /// </summary>
    /// <returns>已审核报价单列表</returns>
    public async Task<List<Quotation>> GetApprovedQuotationsAsync()
    {
        return await Quotations.GetApprovedQuotationsAsync();
    }

    /// <summary>
    /// 拒绝报价单
    /// </summary>
    /// <param name="quotationId">报价单ID</param>
    /// <param name="reason">拒绝原因</param>
    /// <returns></returns>
    public async Task RejectQuotationAsync(long quotationId, string reason)
    {
        var quotation = await Quotations.GetSingleAsync(q => q.Id == quotationId);
        if (quotation.Status == QuotationStatus.PendingApproval)
        {
            quotation.Status = QuotationStatus.Draft;
            quotation.Remark = reason;
            await Quotations.UpdateAsync(quotation);
        }
    }
}
