using PE.Domain.QuotationMng.Entities;
using PE.WebApi.Dtos.QuotationMng;

namespace PE.WebApi.Tests.Fakers.QuotationMng;

internal class QuotationDtoFaker : BaseFaker<QuotationDto>
{
    public QuotationDtoFaker()
    {
        DataFaker = new Faker<QuotationDto>()
            .RuleFor(x => x.Id, f => f.Random.Long(1, 10000))
            .RuleFor(x => x.CompanyId, f => f.Random.Int(1, 1000))
            .RuleFor(x => x.QuotationNo, f => $"QT{f.Date.Recent().ToString("yyyyMMdd")}{f.Random.Int(1000, 9999)}")
            .RuleFor(x => x.Status, f => f.PickRandom<QuotationStatus>())
            .RuleFor(x => x.CreatorId, f => f.Random.Long(1, 100))
            .RuleFor(x => x.CreatorName, f => f.Name.FullName())
            .RuleFor(x => x.CreateTime, f => f.Date.Recent(30))
            .RuleFor(x => x.AuditorId, f => f.Random.Bool() ? f.Random.Int(1, 100) : null)
            .RuleFor(x => x.AuditorName, (f, x) => x.AuditorId.HasValue ? f.Name.FullName() : null)
            .RuleFor(x => x.AuditTime, (f, x) => x.AuditorId.HasValue ? f.Date.Recent(10) : null)
            .RuleFor(x => x.Remark, f => f.Lorem.Sentence())
            .RuleFor(x => x.Packages, f => new ExaminationPackageDtoFaker().Generate(f.Random.Int(1, 3)));
    }
}

internal class ExaminationPackageDtoFaker : BaseFaker<ExaminationPackageDto>
{
    public ExaminationPackageDtoFaker()
    {
        DataFaker = new Faker<ExaminationPackageDto>()
            .RuleFor(x => x.Id, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.QuotationId, f => f.Random.Long(1, 10000))
            .RuleFor(x => x.Name, f => f.Commerce.ProductName())
            .RuleFor(x => x.PackageCode, f => f.Random.AlphaNumeric(8))
            .RuleFor(x => x.OriginalPrice, f => f.Random.Decimal(100, 5000))
            .RuleFor(x => x.DiscountRate, f => f.Random.Decimal(0.5m, 1.0m))
            .RuleFor(x => x.DiscountedPrice, (f, x) => x.OriginalPrice * x.DiscountRate)
            .RuleFor(x => x.Description, f => f.Lorem.Paragraph())
            .RuleFor(x => x.IsActive, f => true)
            .RuleFor(x => x.MinAge, f => f.Random.Int(18, 30))
            .RuleFor(x => x.MaxAge, (f, x) => f.Random.Int(x.MinAge, 80))
            .RuleFor(x => x.GenderType, f => f.PickRandom<GenderType>())
            .RuleFor(x => x.MaritalStatus, f => f.PickRandom<MaritalStatus>())
            .RuleFor(x => x.CombinedItems, f => new ExaminationCombinedItemDtoFaker().Generate(f.Random.Int(1, 5)));
    }

    public List<ExaminationPackageDto> Generate(int count)
    {
        return DataFaker!.Generate(count);
    }
}

internal class ExaminationCombinedItemDtoFaker : BaseFaker<ExaminationCombinedItemDto>
{
    public ExaminationCombinedItemDtoFaker()
    {
        DataFaker = new Faker<ExaminationCombinedItemDto>()
            .RuleFor(x => x.Id, f => f.Random.Long(1, 10000))
            .RuleFor(x => x.PackageId, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.Name, f => f.Commerce.ProductName())
            .RuleFor(x => x.Code, f => f.Random.AlphaNumeric(6))
            .RuleFor(x => x.BasePrice, f => f.Random.Decimal(10, 500))
            .RuleFor(x => x.Category, f => f.PickRandom("内科", "外科", "影像科", "检验科", "妇科"))
            .RuleFor(x => x.Description, f => f.Lorem.Sentence());
    }

    public List<ExaminationCombinedItemDto> Generate(int count)
    {
        return DataFaker!.Generate(count);
    }
}
