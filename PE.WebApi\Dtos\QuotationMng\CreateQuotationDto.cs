namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 创建报价单DTO
/// </summary>
public class CreateQuotationDto
{
    /// <summary>
    /// 团检单位ID
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// 报价说明
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 报价单明细
    /// </summary>
    public List<CreateExaminationPackageDto> Packages { get; set; } = new();
}
