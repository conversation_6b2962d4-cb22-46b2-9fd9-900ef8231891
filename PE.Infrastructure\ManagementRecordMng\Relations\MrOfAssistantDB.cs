﻿using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.ManagementRecordMng.Relations;

public class MrOfDoctorDB(HealthDbConte<PERSON><PERSON> db<PERSON><PERSON><PERSON><PERSON>, Doctor master)
    : BaseRelation<Doctor, ManagementRecord>(db<PERSON><PERSON><PERSON><PERSON>, master), IMrOfDoctor
{
    public override async Task<ManagementRecord> GetSingleAsync(Expression<Func<ManagementRecord, bool>>? expression = null)
    {
        var record = await base.GetSingleAsync(expression) ?? throw new NotFoundException($"管理记录未找到");
        record.Courses = new CourseOfManagementRecordDB(DbContext, record);
        record.Questionnaire = new QuestionnaireOfManagementRecord(DbContext, record);

        return record;
    }
}
