using PE.Domain.QuotationMng.Entities;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 创建检查套餐DTO
/// </summary>
public class CreateExaminationPackageDto
{
    /// <summary>
    /// 套餐ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 套餐名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 套餐编码
    /// </summary>
    public string PackageCode { get; set; } = string.Empty;

    /// <summary>
    /// 套餐原价
    /// </summary>
    public decimal OriginalPrice { get; set; }

    /// <summary>
    /// 折扣率
    /// </summary>
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 折后价
    /// </summary>
    public decimal DiscountedPrice { get; set; }

    /// <summary>
    /// 套餐说明
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 适用年龄下限
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 适用年龄上限
    /// </summary>
    public int MaxAge { get; set; }

    /// <summary>
    /// 适用性别
    /// </summary>
    public GenderType GenderType { get; set; }

    /// <summary>
    /// 适用婚姻状况
    /// </summary>
    public MaritalStatus MaritalStatus { get; set; }

    /// <summary>
    /// 组合项明细
    /// </summary>
    public List<CreateExaminationCombinedItemDto>? CombinedItems { get; set; }
}
