﻿<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  autoReload="true"
	  throwConfigExceptions="true"
	  internalLogLevel="Off"
	  internalLogFile="${basedir}/Logs/internal-nlog/internal-nlog-AspNetCore.txt">
	<targets>
		<!--Error保存至文件-->
		<target name="error_file" xsi:type="File" maxArchiveFiles="30"  encoding="utf-8"
                fileName="${basedir}/Logs/${date:yyyyMMdd}_Error.log"
                archiveFileName="${basedir}/Logs/${date:yyyyMMdd}_Error.{#}.log"
                archiveDateFormat="yyyyMMdd"
                archiveAboveSize="104857600"
                archiveNumbering="Sequence"
                layout="${date:yyyy-MM-dd HH\:mm\:ss} ${mes**ge} ${onexception:${exception:format=tostring} ${newline} ${stacktrace}" />
		<!--Info保存至文件-->
		<target name="info_file" xsi:type="File" maxArchiveFiles="30" encoding="utf-8"
                fileName="${basedir}/Logs/${date:yyyyMMdd}_Info.log"
                archiveFileName="${basedir}/Logs/${date:yyyyMMdd}_Info.{#}.log"
                archiveDateFormat="yyyyMMdd"
                archiveAboveSize="104857600"
                archiveNumbering="Sequence"
                layout="${date:yyyy-MM-dd HH\:mm\:ss} ${uppercase:${level}}： ${mes**ge}" />
		<!--配置Sql Server-->
		<!--<target name="database" xsi:type="Database"
                dbProvider="System.Data.SqlClient.SqlConnection,System.Data.SqlClient"
                connectionString="Data Source=.;Initial Catalog=Log;User ID=**;Password=**;Persist Security Info=True"
                commandText="insert into NLog (Application, Logged, Level, Mes**ge,Logger, CallSite, Exception) values (@Application, @Logged, @Level, @Mes**ge,@Logger, @Callsite, @Exception);">
			<parameter name="@application" layout="AspNetCoreNlog" />
			<parameter name="@logged" layout="${date}" />
			<parameter name="@level" layout="${level}" />
			<parameter name="@mes**ge" layout="${mes**ge}" />
			<parameter name="@logger" layout="${logger}" />
			<parameter name="@callSite" layout="${callsite:filename=true}" />
			<parameter name="@exception" layout="${exception:tostring}" />
		</target>-->
		<target name="console" xsi:type="Console" layout="${longdate} ${level} ${mes**ge} ${exception}" />
	</targets>
	<rules>
		<!-- add your logging rules here -->
		<logger name="*" minlevel="Info" maxlevel="Warn" writeTo="info_file" />
		<!--<logger name="*" minlevel="Debug" writeTo="debugger" />-->
		<logger name="*" minlevel="Error" writeTo="error_file" />
		<!--配置Sql Server-->
		<!--<logger name="*" minlevel="Info" writeTo="database" />-->
		<logger name="*" minlevel="Info" writeTo="console" />
	</rules>
</nlog>