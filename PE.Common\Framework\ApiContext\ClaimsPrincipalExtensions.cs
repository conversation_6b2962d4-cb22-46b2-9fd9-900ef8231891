﻿using System.Security.Claims;

namespace PE.Common.Framework.ApiContext
{
    public static class ClaimsPrincipalExtensions
    {
        public static ApiUser ToApiUser(this ClaimsPrincipal user)
        {
            if (user is null || !user.Claims.Any())
            {
                return new ApiUser();
            }

            return new ApiUser
            {
                Id = user.Claims.FirstOrDefault(c => c.Type == HealthClaimTypes.Id)!.Value,
                //Name = user.Claims.FirstOrDefault(c => c.Type == HealthClaimTypes.Name)!.Value,
                Role = user.Claims.FirstOrDefault(c => c.Type == HealthClaimTypes.Role)!.Value
            };
        }
    }
}
