﻿using System.Linq.Expressions;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.ManagementRecordMng.Relations;

public class CourseOfManagementRecordDB(HealthDbContext dbContext, ManagementRecord master)
    : BaseRelation<ManagementRecord, Course>(db<PERSON>ontext, master), ICourseOfManagementRecord
{
    public async Task<bool> HasInProgressAsync()
    {
        return await DbContext.Courses.AsQueryable()
            .Where(x => x.ManagementRecordId == Master.Id)
            .AnyAsync(x => x.Status == CourseStatus.InProgress || x.Status == CourseStatus.New);
    }

    public async Task<List<Course>> GetAllAsync()
    {
        return await DbContext.Courses.AsQueryable()
            .Where(x => x.ManagementRecordId == Master.Id)
            .OrderByDescending(x => x.SerialNo)
            .ToListAsync();
    }

    public override async Task<Course> GetSingleAsync(Expression<Func<Course, bool>>? expression = null)
    {
        var course = await base.GetSingleAsync(expression) ?? throw new NotFoundException("疗程信息未找到");
        course.Schedules = new ScheduleOfCourseDB(DbContext, course);

        return course;
    }


    public override async Task AddAsync(Course course)
    {
        course.Id = await DbContext.Courses.InsertReturnBigIdentityAsync(course);
        course.Schedules = new ScheduleOfCourseDB(DbContext, course);
    }
}
