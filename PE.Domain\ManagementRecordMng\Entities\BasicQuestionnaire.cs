﻿namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 基础问卷
/// </summary>
[SugarTable("ques_basic_questionnaire", TableDescription = "基础问卷")]
[SugarIndex("index_{table}_management_record_id", nameof(ManagementRecordId), OrderByType.Desc)]
public class BasicQuestionnaire : Entity
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 管理记录Id
    /// </summary>
    [SugarColumn(ColumnName = "management_record_id", ColumnDescription = "管理记录Id")]
    public long ManagementRecordId { get; set; }

    /// <summary>
    /// 答题卡
    /// </summary>
    [SugarColumn(ColumnName = "answer", ColumnDescription = "答题卡")]
    public string Answer { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    [SugarColumn(ColumnName = "version", ColumnDescription = "版本号")]
    public int Version { get; set; }

    ///// <summary>
    ///// 创建时间
    ///// </summary>
    //[SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    //public DateTime CreationTime { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
