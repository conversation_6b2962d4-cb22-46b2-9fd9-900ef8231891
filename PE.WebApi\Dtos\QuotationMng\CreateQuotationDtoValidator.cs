using FluentValidation;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 创建报价单DTO验证器
/// </summary>
public class CreateQuotationDtoValidator : AbstractValidator<CreateQuotationDto>
{
    public CreateQuotationDtoValidator()
    {
        RuleFor(x => x.CompanyId)
            .GreaterThan(0)
            .WithMessage("团检客户ID必须大于0");

        RuleFor(x => x.Remark)
            .MaximumLength(500)
            .WithMessage("报价说明不能超过500个字符");

        RuleFor(x => x.Packages)
            .NotNull()
            .WithMessage("报价单明细不能为空")
            .Must(packages => packages != null && packages.Count > 0)
            .WithMessage("报价单明细至少包含一个套餐");

        RuleForEach(x => x.Packages)
            .SetValidator(new CreateExaminationPackageDtoValidator());
    }
}
