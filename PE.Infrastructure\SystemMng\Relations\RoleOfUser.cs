﻿using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.SystemMng.Entities;
using PE.Domain.SystemMng.Relations;

namespace PE.Infrastructure.SystemMng.Relations
{
    public class RoleOfUser(HealthDbContext db<PERSON>onte<PERSON><PERSON>, Doctor master)
        : BaseRelation<Doctor, Role>(db<PERSON><PERSON><PERSON><PERSON>, master), IRoleOfUser
    {
        public override async Task<Role> GetSingleAsync(Expression<Func<Role, bool>>? expression = null)
        {
            return await DbContext.Roles.AsQueryable()
                .WhereIF(expression is not null, expression)
                .Where(r => r.Id == Master.RoleId)
                .SingleAsync();
        }
    }
}
