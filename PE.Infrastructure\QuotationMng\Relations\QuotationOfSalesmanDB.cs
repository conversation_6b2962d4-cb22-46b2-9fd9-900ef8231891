using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;

namespace PE.Infrastructure.QuotationMng.Relations;

public class QuotationOfSalesmanDB(HealthDbContext db<PERSON><PERSON><PERSON><PERSON>, Doctor master)
    : BaseRelation<Doctor, Quotation>(db<PERSON><PERSON><PERSON><PERSON>, master), IQuotationOfSalesman
{
    public async Task<List<Quotation>> GetMyQuotationsAsync()
    {
        return await DbContext.Db.Queryable<Quotation>()
            .Where(q => q.CreatorId == Master.Id)
            .OrderByDescending(q => q.CreateTime)
            .ToListAsync();
    }

    public async Task<List<Quotation>> GetDraftQuotationsAsync()
    {
        return await DbContext.Db.Queryable<Quotation>()
            .Where(q => q.CreatorId == Master.Id && q.Status == QuotationStatus.Draft)
            .OrderByDescending(q => q.CreateTime)
            .ToListAsync();
    }

    public override async Task AddAsync(Quotation quotation)
    {
        quotation.CreatorId = Master.Id;
        quotation.CreatorName = Master.Name!;
        quotation.Id = await DbContext.Db.Insertable(quotation).ExecuteReturnBigIdentityAsync();
        quotation.Packages = new PackageOfQuotationDB(DbContext, quotation);
    }
}
