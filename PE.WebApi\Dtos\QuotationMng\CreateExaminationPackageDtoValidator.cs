using FluentValidation;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 创建检查套餐DTO验证器
/// </summary>
public class CreateExaminationPackageDtoValidator : AbstractValidator<CreateExaminationPackageDto>
{
    public CreateExaminationPackageDtoValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("套餐ID不能为空")
            .MaximumLength(50)
            .WithMessage("套餐ID不能超过50个字符");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("套餐名称不能为空")
            .MaximumLength(100)
            .WithMessage("套餐名称不能超过100个字符");

        RuleFor(x => x.PackageCode)
            .NotEmpty()
            .WithMessage("套餐编码不能为空")
            .MaximumLength(50)
            .WithMessage("套餐编码不能超过50个字符");

        RuleFor(x => x.OriginalPrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage("套餐原价不能小于0");

        RuleFor(x => x.DiscountRate)
            .InclusiveBetween(0, 1)
            .WithMessage("折扣率必须在0到1之间");

        RuleFor(x => x.DiscountedPrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage("折后价不能小于0");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("套餐说明不能超过500个字符");

        RuleFor(x => x.MinAge)
            .GreaterThanOrEqualTo(0)
            .WithMessage("适用年龄下限不能小于0");

        RuleFor(x => x.MaxAge)
            .GreaterThanOrEqualTo(0)
            .WithMessage("适用年龄上限不能小于0");

        RuleFor(x => x)
            .Must(x => x.MaxAge >= x.MinAge)
            .WithMessage("适用年龄上限不能小于下限");

        RuleForEach(x => x.CombinedItems)
            .SetValidator(new CreateExaminationCombinedItemDtoValidator());
    }
}
