﻿namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 日程明细
/// </summary>
[SugarTable("physio_schedule_detail", TableDescription = "理疗日程明细")]
[SugarIndex("index_{table}_course_id", nameof(CourseId), OrderByType.Desc)]
[SugarIndex("index_{table}_schedule_id", nameof(ScheduleId), OrderByType.Desc)]
public class ScheduleDetail : Entity
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    [SugarColumn(ColumnName = "course_id", ColumnDescription = "疗程Id")]
    public long CourseId { get; set; }

    [SugarColumn(ColumnName = "schedule_id", ColumnDescription = "日程ID")]
    public long ScheduleId { get; set; }

    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目ID")]
    public long ItemId { get; set; }

    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称")]
    public string? ItemName { get; set; }

    [SugarColumn(ColumnName = "item_remark", ColumnDescription = "项目备注")]
    public string? ItemRemark { get; set; }

    [SugarColumn(ColumnName = "is_complete", ColumnDescription = "是否完成")]
    public bool IsComplete { get; set; }

    [SugarColumn(ColumnName = "beging_time", ColumnDescription = "开始时间")]
    public DateTime? BegingTime { get; set; }

    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public DateTime? EndTime { get; set; }

    [SugarColumn(ColumnName = "exec_remark", ColumnDescription = "执行备注")]
    public string? ExecRemark { get; set; }

    [SugarColumn(ColumnName = "physio_id", ColumnDescription = "理疗师Id")]
    public long? PhysioId { get; set; }

    [SugarColumn(ColumnName = "physio_name", ColumnDescription = "理疗师姓名")]
    public string? PhysioName { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}


//public class DetailExecRecord
//{
//    public long DetailId { get; set; }

//    public DateTime ExecTime { get; set; }

//    public string ExecRemark { get; set; }

//    public long PhysioId { get; set; }

//    public string PhysioName { get; set; }
//}


///// <summary>
///// 日程明细
///// </summary>
//public class ScheduleDetail
//{
//    public long Id { get; set; }

//    public long ScheduleId { get; set; }

//    public long ItemId { get; set; }

//    public string? ItemName { get; set; }

//    public string? ItemRemark { get; set; }
//}
