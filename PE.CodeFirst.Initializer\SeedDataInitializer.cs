﻿using PE.CodeFirst.Initializer.SeedData;
using SqlSugar;
using System.Reflection;

namespace PE.CodeFirst.Initializer
{
    public class SeedDataInitializer(string connecetionString, DbType dbType)
    {
        private readonly string _connecetionString = connecetionString;
        private readonly DbType _dbType = dbType;


        public void Init()
        {
            var db = DbClientFactory.GetClient(_connecetionString, _dbType);

            var implementingTypes = Assembly.GetExecutingAssembly().GetTypes()
            .Where(type => type.GetInterfaces()
                .Any(iface => iface == typeof(IDataInitializer)))
            .ToList();

            foreach (Type implType in implementingTypes)
            {
                object? instance = Activator.CreateInstance(implType);
                MethodInfo? initMethod = implType.GetMethod("Init");
                try
                {
                    initMethod!.Invoke(instance, [db]);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($" {instance!.GetType().Name} 初始化种子数据失败");
                    Console.WriteLine($" 异常：{ex.Message}");
                }
            }
        }

        public void Init<T>()
        {
            var db = DbClientFactory.GetClient(_connecetionString, _dbType);

            var implType = Assembly.GetExecutingAssembly().GetTypes()
            .Where(type => type.GetInterfaces()
                .Any(iface => iface == typeof(IDataInitializer)))
            .Where(type => type.Name.StartsWith(typeof(T).Name))
            .First();

            object? instance = Activator.CreateInstance(implType);
            MethodInfo? initMethod = implType.GetMethod("Init");
            initMethod!.Invoke(instance, [db]);
        }
    }
}
