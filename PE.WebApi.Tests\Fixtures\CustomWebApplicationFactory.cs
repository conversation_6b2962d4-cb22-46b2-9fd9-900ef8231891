﻿//#define USE_SQLSERVER

using PE.CodeFirst.Initializer;
using PE.WebApi.Tests.DockerAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace PE.WebApi.Tests.Fixtures
{
    public class CustomWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
    {

#if USE_SQLSERVER
        readonly DbContainer4MsSql _dbContainer = new();
#else
        readonly DbContainer4PostgreSql _dbContainer = new();
#endif


        public HttpClient CreateClient(string token)
        {
            var client = CreateClient();
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            return client;
        }


        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureLogging(logging =>
            {
                logging.ClearProviders();
            });

            builder.ConfigureServices(services =>
            {
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(ISqlSugarClient));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                services.AddScoped<ISqlSugarClient>(p =>
                {
                    return new SqlSugarClient(
                        new ConnectionConfig()
                        {
                            DbType = DbConfig.GetDbType(),
                            ConnectionString = DbConfig.GetConnectionString(),
                            IsAutoCloseConnection = true,
                        },
                        db =>
                        {
                            db.Aop.OnLogExecuting = (sql, pars) =>
                            {
                                Console.WriteLine(sql + "\r\n" + db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
                            };
                        }
                    );
                });

                services.AddAuthorization(options =>
                {
                    options.DefaultPolicy = new AuthorizationPolicyBuilder()
                        .RequireAssertion(_ => true)
                        .Build();
                    options.FallbackPolicy = options.DefaultPolicy;
                });
            });
        }


        async Task IAsyncLifetime.InitializeAsync()
        {
            await _dbContainer.StartAsync();

            DbConfig.SetConnectionString(_dbContainer.GetConnectionString());
            DbConfig.SetDbType(_dbContainer.GetDbType());

            DbInitializer initializer = new(DbConfig.GetConnectionString(), DbConfig.GetDbType());
            initializer.Init();
        }

        async Task IAsyncLifetime.DisposeAsync()
        {
            await _dbContainer.DisposeAsync();
        }


        /*
        //使用本地数据库
        //Task IAsyncLifetime.InitializeAsync()
        //{
        //    DbInitializer initializer = new(DbConfig.GetConnectionString(), DbConfig.GetDbType());
        //    initializer.Init();
        //    return Task.CompletedTask;
        //}

        //Task IAsyncLifetime.DisposeAsync()
        //{
        //    DbCleaner cleaner = new(DbConfig.GetConnectionString(), DbConfig.GetDbType());
        //    cleaner.Clean();
        //    return Task.CompletedTask;
        //}
        */
    }
}
