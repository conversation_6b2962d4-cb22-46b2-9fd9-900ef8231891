﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng;
using PE.Domain.SystemMng;

namespace PE.Domain.DoctorMng.Repositories;

public interface IDoctorRepository : IBaseRepository<Doctor>
{
    IUserContext InUserContext();

    IManagementContext InManagementContext();
}


//public static class ManagementContextExtensions
//{
//    public static IManagementContext InManagementContext(this IUserContext context)
//    {
//        return new ManagementContext(context.DbContext, context.User);
//    }
//}