using System.Linq.Expressions;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Repositories;
using PE.Infrastructure.QuotationMng.Relations;
using PE.Common.Exceptions;
using PE.Common.Utils;

namespace PE.Infrastructure.QuotationMng.Repositories;

public class QuotationRepository(HealthDbContext dbContext) : BaseRepository<Quotation>(dbContext), IQuotationRepository
{
    public override async Task<Quotation> GetSingleAsync(Expression<Func<Quotation, bool>>? expression = null)
    {
        var quotation = await base.GetSingleAsync(expression) ?? throw new NotFoundException($"报价单未找到");
        quotation.Packages = new PackageOfQuotationDB(DbContext, quotation);

        return quotation;
    }

    public override async Task AddAsync(Quotation entity)
    {
        entity.Id = await DbContext.Db.Insertable(entity).ExecuteReturnBigIdentityAsync();
        entity.Packages = new PackageOfQuotationDB(DbContext, entity);
    }

    /// <summary>
    /// 分页查询报价单，支持查询条件
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="keyword">关键字（报价单号）</param>
    /// <param name="status">状态</param>
    /// <param name="companyId">团检单位ID</param>
    /// <param name="creatorId">创建人ID</param>
    /// <param name="createTimeStart">创建开始时间</param>
    /// <param name="createTimeEnd">创建结束时间</param>
    /// <returns>分页结果</returns>
    public async Task<(List<Quotation>, int)> GetPageAsync(
        int pageIndex,
        int pageSize,
        string? keyword = null,
        QuotationStatus? status = null,
        int? companyId = null,
        long? creatorId = null,
        DateTime? createTimeStart = null,
        DateTime? createTimeEnd = null)
    {
        var totalCount = new RefAsync<int>();

        var list = await DbContext.Quotations.AsQueryable()
            .WhereIF(keyword.IsNotNullOrEmpty(), q => q.QuotationNo.Contains(keyword!))
            .WhereIF(status.HasValue, q => q.Status == status!.Value)
            .WhereIF(companyId.HasValue, q => q.CompanyId == companyId!.Value)
            .WhereIF(creatorId.HasValue, q => q.CreatorId == creatorId!.Value)
            .WhereIF(createTimeStart.HasValue, q => q.CreateTime >= createTimeStart!.Value)
            .WhereIF(createTimeEnd.HasValue, q => q.CreateTime <= createTimeEnd!.Value)
            .OrderByDescending(q => q.CreateTime)
            .ToPageListAsync(pageIndex, pageSize, totalCount);

        return (list, totalCount);
    }
}
