using System.Linq.Expressions;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Repositories;
using PE.Infrastructure.QuotationMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.QuotationMng.Repositories;

public class QuotationRepository(HealthDbContext dbContext) : BaseRepository<Quotation>(dbContext), IQuotationRepository
{
    public override async Task<Quotation> GetSingleAsync(Expression<Func<Quotation, bool>>? expression = null)
    {
        var quotation = await base.GetSingleAsync(expression) ?? throw new NotFoundException($"报价单未找到");
        quotation.Packages = new PackageOfQuotationDB(DbContext, quotation);

        return quotation;
    }

    public override async Task AddAsync(Quotation entity)
    {
        entity.Id = await DbContext.Db.Insertable(entity).ExecuteReturnBigIdentityAsync();
        entity.Packages = new PackageOfQuotationDB(DbContext, entity);
    }
}
