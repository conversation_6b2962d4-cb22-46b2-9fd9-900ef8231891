using FluentValidation;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 更新检查项目组合明细DTO验证器
/// </summary>
public class UpdateExaminationCombinedItemDtoValidator : AbstractValidator<UpdateExaminationCombinedItemDto>
{
    public UpdateExaminationCombinedItemDtoValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("项目ID必须大于0");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("项目名称不能为空")
            .MaximumLength(100)
            .WithMessage("项目名称不能超过100个字符");

        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("项目编码不能为空")
            .MaximumLength(50)
            .WithMessage("项目编码不能超过50个字符");

        RuleFor(x => x.BasePrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage("基础单价不能小于0");

        RuleFor(x => x.Category)
            .NotEmpty()
            .WithMessage("项目类别不能为空")
            .MaximumLength(50)
            .WithMessage("项目类别不能超过50个字符");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("项目描述不能超过500个字符");
    }
}
