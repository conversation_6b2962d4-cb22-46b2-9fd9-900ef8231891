﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.DoctorMng.Repositories;
using PE.WebApi.Dtos.DoctorMng;
using PE.WebApi.Middlewares;
using PE.Common.Framework.Encryption;
using Microsoft.AspNetCore.Mvc;

namespace PE.WebApi.Controllers.DoctorMng;

//[Authorize]
public class DoctorController(IMapper mapper, IDoctorRepository doctorRepository) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;


    /// <summary>
    /// 添加医生
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [HttpPost("create")]
    //[TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateDoctorRequest>))]
    [ValidateDto(typeof(CreateDoctorRequest), typeof(CreateDoctorRequestValidator))]
    public async Task<ActionResult<DoctorDto>> Create([FromBody] CreateDoctorRequest req)
    {
        Doctor? user = await _doctorRepository.GetSingleAsync(x => x.UserName == req.UserName);
        if (user is not null)
        {
            return Problem400Result("用户名已存在");
        }

        req.Password = Md5Encryptor.GetMD5Hash(req.Password!, Md5Encryptor.DefaultSalt);
        Doctor doctor = _mapper.Map<Doctor>(req);
        doctor.Id = await _doctorRepository.AddReturnBigIdentityAsync(doctor);

        return Ok(_mapper.Map<DoctorDto>(doctor));
    }

    /// <summary>
    /// 更新医生信息
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("update")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<DoctorDto>))]
    public async Task<ActionResult> Update([FromBody] DoctorDto dto)
    {
        var doctor = await _doctorRepository.GetSingleAsync(x => x.Id == dto.Id);
        if (doctor is null)
            return Problem404Result();

        doctor.Name = dto.Name!;
        //doctor.AutographUrl = dto.AutographUrl;
        doctor.RoleId = dto.RoleId;

        await _doctorRepository.UpdateAsync(doctor);

        return Ok();
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <returns></returns>
    [HttpPost("updatePassword")]
    public async Task<ActionResult> UpdatePassword([FromBody] UpdatePasswordRequest dto)
    {
        var doctor = await _doctorRepository.GetSingleAsync(x => x.Id == dto.Id);
        if (doctor is null)
            return Problem404Result();

        doctor.Password = Md5Encryptor.GetMD5Hash(dto.Password!, Md5Encryptor.DefaultSalt);
        await _doctorRepository.UpdateAsync(doctor);

        return Ok();
    }

    /// <summary>
    /// 获取医生列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("list")]
    public async Task<ActionResult<List<DoctorDto>>> GetList()
    {
        var doctors = await _doctorRepository.GetListAsync();

        return Ok(_mapper.Map<List<DoctorDto>>(doctors.OrderByDescending(d => d.Id)));
    }

    /// <summary>
    /// 获取医生信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    public async Task<ActionResult<DoctorDto>> Get(long id)
    {
        var doctor = await _doctorRepository.GetSingleAsync(x => x.Id == id);
        if (doctor is null)
            return Problem404Result();

        return Ok(_mapper.Map<DoctorDto>(doctor));
    }
}
