using PE.Domain.QuotationMng.Entities;
using PE.Infrastructure.QuotationMng.Relations;
using PE.Infrastructure;
using Moq;
using SqlSugar;

namespace PE.Domain.Tests.QuotationMng.Relations;

public class PackageOfQuotationDBTests
{
    private readonly Mock<HealthDbContext> _mockDbContext = new();
    private readonly Quotation _quotation = new() { Id = 1, QuotationNo = "QT001" };

    private PackageOfQuotationDB CreateRelation() => new(_mockDbContext.Object, _quotation);

    [Fact]
    public async Task GetAllAsync_ShouldReturnPackagesOrderedByCode()
    {
        // Arrange
        var packages = new List<ExaminationPackage>
        {
            new() { Id = "PKG002", PackageCode = "B001", QuotationId = 1 },
            new() { Id = "PKG001", PackageCode = "A001", QuotationId = 1 }
        };

        var mockQueryable = new Mock<ISugarQueryable<ExaminationPackage>>();
        var mockDb = new Mock<ISqlSugarClient>();

        mockDb.Setup(db => db.Queryable<ExaminationPackage>())
            .Returns(mockQueryable.Object);
        mockQueryable.Setup(q => q.Where(It.IsAny<System.Linq.Expressions.Expression<Func<ExaminationPackage, bool>>>()))
            .Returns(mockQueryable.Object);
        //mockQueryable.Setup(q => q.OrderBy(It.IsAny<System.Linq.Expressions.Expression<Func<ExaminationPackage, object>>>()))
        //    .Returns(mockQueryable.Object);
        mockQueryable.Setup(q => q.ToListAsync())
            .ReturnsAsync(packages.OrderBy(p => p.PackageCode).ToList());

        _mockDbContext.Setup(ctx => ctx.Db)
            .Returns(mockDb.Object);

        // Act
        var relation = CreateRelation();
        var result = await relation.GetAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Equal("A001", result[0].PackageCode);
        Assert.Equal("B001", result[1].PackageCode);
    }

    [Fact]
    public async Task AddAsync_ShouldSetQuotationIdAndCombinedItems()
    {
        // Arrange
        var package = new ExaminationPackage { Id = "PKG001", Name = "基础套餐" };
        
        var mockDb = new Mock<ISqlSugarClient>();
        var mockInsertable = new Mock<IInsertable<ExaminationPackage>>();
        
        mockDb.Setup(db => db.Insertable(package))
            .Returns(mockInsertable.Object);
        mockInsertable.Setup(i => i.ExecuteCommandAsync())
            .ReturnsAsync(1);

        _mockDbContext.Setup(ctx => ctx.Db)
            .Returns(mockDb.Object);

        // Act
        var relation = CreateRelation();
        await relation.AddAsync(package);

        // Assert
        Assert.Equal(_quotation.Id, package.QuotationId);
        Assert.NotNull(package.CombinedItems);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldSetQuotationIdForAllPackages()
    {
        // Arrange
        var packages = new List<ExaminationPackage>
        {
            new() { Id = "PKG001", Name = "基础套餐" },
            new() { Id = "PKG002", Name = "高级套餐" }
        };
        
        var mockDb = new Mock<ISqlSugarClient>();
        var mockInsertable = new Mock<IInsertable<ExaminationPackage>>();
        
        mockDb.Setup(db => db.Insertable(packages))
            .Returns(mockInsertable.Object);
        mockInsertable.Setup(i => i.ExecuteCommandAsync())
            .ReturnsAsync(2);

        _mockDbContext.Setup(ctx => ctx.Db)
            .Returns(mockDb.Object);

        // Act
        var relation = CreateRelation();
        await relation.AddRangeAsync(packages);

        // Assert
        Assert.All(packages, package =>
        {
            Assert.Equal(_quotation.Id, package.QuotationId);
            Assert.NotNull(package.CombinedItems);
        });
    }

    [Fact]
    public void Constructor_ShouldSetMasterAndDbContext()
    {
        // Act
        var relation = CreateRelation();

        // Assert
        Assert.Equal(_quotation, relation.Master);
        Assert.Equal(_mockDbContext.Object, relation.DbContext);
    }
}
