﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;

namespace PE.Domain.QuotationMng.Roles;

/// <summary>
/// 业务员
/// </summary>
/// <param name="doctor">医生实体</param>
/// <param name="quotations">报价单关系</param>
public class Salesman(Doctor doctor, IQuotationOfSalesman quotations)
{
    public Doctor Doctor { get; } = doctor;
    public IQuotationOfSalesman Quotations { get; } = quotations;

    /// <summary>
    /// 创建报价单
    /// </summary>
    /// <param name="quotation">报价单实体</param>
    /// <returns></returns>
    public async Task CreateQuotationAsync(Quotation quotation)
    {
        quotation.Status = QuotationStatus.Draft;
        quotation.CreatorId = Doctor.Id;
        quotation.CreatorName = Doctor.Name!;
        quotation.CreateTime = DateTime.Now;
        quotation.QuotationNo = GenerateQuotationNo();

        await Quotations.AddAsync(quotation);
    }

    /// <summary>
    /// 提交报价单审核
    /// </summary>
    /// <param name="quotationId">报价单ID</param>
    /// <returns></returns>
    public async Task SubmitForApprovalAsync(long quotationId)
    {
        var quotation = await Quotations.GetSingleAsync(q => q.Id == quotationId);
        if (quotation.Status == QuotationStatus.Draft)
        {
            quotation.Status = QuotationStatus.PendingApproval;
            await Quotations.UpdateAsync(quotation);
        }
    }

    /// <summary>
    /// 获取我的报价单列表
    /// </summary>
    /// <returns>报价单列表</returns>
    public async Task<List<Quotation>> GetMyQuotationsAsync()
    {
        return await Quotations.GetMyQuotationsAsync();
    }

    /// <summary>
    /// 获取草稿报价单列表
    /// </summary>
    /// <returns>草稿报价单列表</returns>
    public async Task<List<Quotation>> GetDraftQuotationsAsync()
    {
        return await Quotations.GetDraftQuotationsAsync();
    }

    /// <summary>
    /// 生成报价单号
    /// </summary>
    /// <returns>报价单号</returns>
    private static string GenerateQuotationNo()
    {
        return "QT" + DateTime.Now.ToString("yyyyMMddHHmmss");
    }
}
