﻿namespace PE.WebApi.Tests.Utils
{
    internal class Postman(HttpClient client, string url)
    {
        public HttpClient Client { get; private set; } = client;
        public string Url { get; private set; } = url;


        public async Task<HttpResponseMessage> PostAsync<TDto>(TDto dto)
        {
            return await Client.PostAsJsonAsync(Url, dto);
        }

        public async Task<HttpResponseMessage> PostAndAssertAsync<TDto>(TDto dto, HttpStatusCode statusCode = HttpStatusCode.OK)
        {
            HttpResponseMessage rsp = await Client.PostAsJsonAsync(Url, dto);

            rsp.StatusCode.Should().Be(statusCode);

            return rsp;
        }
    }
}
