﻿using PE.Domain.QuotationMng.Entities;

namespace PE.Domain.QuotationMng.Repositories;

public interface IQuotationRepository : IBaseRepository<Quotation>
{
    /// <summary>
    /// 分页查询报价单，支持查询条件
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="keyword">关键字（报价单号）</param>
    /// <param name="status">状态</param>
    /// <param name="companyId">团检单位ID</param>
    /// <param name="creatorId">创建人ID</param>
    /// <param name="createTimeStart">创建开始时间</param>
    /// <param name="createTimeEnd">创建结束时间</param>
    /// <returns>分页结果</returns>
    Task<(List<Quotation>, int)> GetPageAsync(
        int pageIndex,
        int pageSize,
        string? keyword = null,
        QuotationStatus? status = null,
        int? companyId = null,
        long? creatorId = null,
        DateTime? createTimeStart = null,
        DateTime? createTimeEnd = null);
}
