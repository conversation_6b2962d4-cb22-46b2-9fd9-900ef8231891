using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Repositories;
using PE.Domain.QuotationMng.Roles;
using PE.Infrastructure.QuotationMng;
using PE.Infrastructure;
using Moq;

namespace PE.Domain.Tests.QuotationMng;

public class QuotationContextTests
{
    private readonly Mock<HealthDbContext> _mockDbContext = new();
    private readonly Mock<IQuotationRepository> _mockQuotationRepository = new();
    private readonly Doctor _doctor = new() { Id = 1, Name = "张三" };

    private QuotationContext CreateContext() => new(_mockDbContext.Object, _mockQuotationRepository.Object);

    [Fact]
    public void AsSalesman_ShouldReturnSalesmanWithCorrectDoctor()
    {
        // Act
        var context = CreateContext();
        var salesman = context.AsSalesman(_doctor);

        // Assert
        Assert.NotNull(salesman);
        Assert.IsType<Salesman>(salesman);
        Assert.Equal(_doctor, salesman.Doctor);
        Assert.NotNull(salesman.Quotations);
    }

    [Fact]
    public void AsApprover_ShouldReturnApproverWithCorrectDoctor()
    {
        // Act
        var context = CreateContext();
        var approver = context.AsApprover(_doctor);

        // Assert
        Assert.NotNull(approver);
        Assert.IsType<Approver>(approver);
        Assert.Equal(_doctor, approver.Doctor);
        Assert.NotNull(approver.Quotations);
    }

    [Fact]
    public void Include_Quotation_ShouldSetPackagesRelation()
    {
        // Arrange
        var quotation = new Quotation { Id = 1 };

        // Act
        var context = CreateContext();
        context.Include(quotation);

        // Assert
        Assert.NotNull(quotation.Packages);
    }

    [Fact]
    public void Include_ExaminationPackage_ShouldSetCombinedItemsRelation()
    {
        // Arrange
        var package = new ExaminationPackage { Id = "PKG001" };

        // Act
        var context = CreateContext();
        context.Include(package);

        // Assert
        Assert.NotNull(package.CombinedItems);
    }

    [Fact]
    public void Constructor_ShouldSetProperties()
    {
        // Act
        var context = CreateContext();

        // Assert
        Assert.Equal(_mockDbContext.Object, context.DbContext);
        Assert.Equal(_mockQuotationRepository.Object, context.QuotationRepository);
    }
}
