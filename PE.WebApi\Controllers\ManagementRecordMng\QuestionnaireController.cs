﻿using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.WebApi.Dtos.ManagementRecordMng;

namespace PE.WebApi.Controllers.ManagementRecordMng;

public class QuestionnaireController(IMapper mapper, IManagementRecordRepository recordRepository, IDoctorRepository doctorRepository)
    : BaseController
{
    /// <summary>
    /// 创建基础问卷信息
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("create")]
    public async Task<ActionResult<QuestionnaireResponseDto>> Create([FromBody] QuestionnaireDto dto)
    {
        var ques = mapper.Map<PersonalInformationQuestionnaire>(dto);
        var dr = await doctorRepository.GetSingleAsync(d => d.Id == dto.AssistantId);
        if (dr == null)
        {
            return Problem404Result($"ID为{dto.AssistantId}的医生未找到");
        }
        var assistant = doctorRepository.InManagementContext().AsAssistant(dr);
        await assistant.CreateQuestionnaireAsync(ques);

        return Ok(mapper.Map<QuestionnaireResponseDto>(ques));
    }


    /// <summary>
    /// 更新基础问卷信息
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("update")]
    public async Task<ActionResult> Update([FromBody] QuestionnaireDto dto)
    {
        var ques = mapper.Map<PersonalInformationQuestionnaire>(dto);
        var dr = await doctorRepository.GetSingleAsync(d => d.Id == dto.AssistantId);
        if (dr == null)
        {
            return Problem404Result($"ID为{dto.AssistantId}的医生未找到");
        }
        var assistant = doctorRepository.InManagementContext().AsAssistant(dr);
        await assistant.UpdateQuestionnaireAsync(ques);

        return Ok();
    }


    /// <summary>
    /// 查询基础问卷信息
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpGet]
    public async Task<ActionResult<QuestionnaireResponseDto>> Get([FromQuery] QuestionnaireQuery query)
    {
        var record = await recordRepository.GetSingleAsync(m => m.Id == query.ManagementRecordId);

        var questionnaire = await record.Questionnaire.GetSingleAsync();

        if (questionnaire == null)
            return Problem404Result("问卷信息未找到");

        return Ok(mapper.Map<QuestionnaireResponseDto>(questionnaire));
    }
}
