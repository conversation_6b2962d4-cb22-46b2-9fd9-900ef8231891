﻿namespace PE.WebApi.Dtos.SystemMng
{
    /// <summary>
    /// 登录信息
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }
    }


    /// <summary>
    /// 登录返回信息
    /// </summary>
    /// <param name="user">登录的用户信息</param>
    /// <param name="accessToken">jwt</param>
    /// <param name="expiresIn">相对过期时间（秒）</param>
    /// <param name="menuIds">菜单Id列表</param>
    public class LoginResult<T>(T user, string accessToken, int expiresIn, int[] menuIds)
    {
        /// <summary>
        /// jwt
        /// </summary>
        public string AccessToken { get; set; } = accessToken;

        /// <summary>
        /// 相对过期时间（秒）
        /// </summary>
        public int ExpiresIn { get; set; } = expiresIn;

        /// <summary>
        /// 菜单Id列表
        /// </summary>
        public int[] MenuIds { get; set; } = menuIds;

        /// <summary>
        /// 用户信息
        /// </summary>
        public T User { get; set; } = user;
    }
}
