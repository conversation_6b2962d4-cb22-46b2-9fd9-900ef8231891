using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Tests.Fakers.QuotationMng;
using Humanizer;

namespace PE.WebApi.Tests.QuotationMng.QuotationController;

[Collection(nameof(DatabaseCollection))]
public class Create_QuotationControllerTests(CustomWebApplicationFactory factory) : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly CreateQuotationDtoFaker _faker = new();
    private Doctor? _stubDoctor;

    [Fact]
    public async Task Create_返回创建的报价单_当数据有效时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var dto = _faker.Generate();
        var url = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";

        // Act
        var response = await client.PostAsJsonAsync(url, dto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<QuotationDto>();
        result.Should().NotBeNull();
        result!.CompanyId.Should().Be(dto.CompanyId);
        result.Remark.Should().Be(dto.Remark);
        result.QuotationNo.Should().NotBeNullOrEmpty();
        result.Status.Should().Be(Domain.QuotationMng.Entities.QuotationStatus.Draft);
        result.CreatorId.Should().Be(_stubDoctor.Id);
        result.CreatorName.Should().Be(_stubDoctor.Name);
    }

    [Fact]
    public async Task Create_返回404_当业务员不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var dto = _faker.Generate();
        var nonExistentSalesmanId = 99999;
        var url = $"{Urls.Quotation.Create}?salesmanId={nonExistentSalesmanId}";

        // Act
        var response = await client.PostAsJsonAsync(url, dto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task Create_返回400_当团检单位ID无效时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var dto = _faker.Generate();
        dto.CompanyId = 0; // 无效的单位ID
        var url = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";

        // Act
        var response = await client.PostAsJsonAsync(url, dto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Create_返回400_当套餐列表为空时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var dto = _faker.Generate();
        dto.Packages = new List<CreateExaminationPackageDto>(); // 空套餐列表
        var url = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";

        // Act
        var response = await client.PostAsJsonAsync(url, dto);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    public async Task InitializeAsync()
    {
        // 创建测试用的医生数据
        var client = _factory.CreateClient();
        var doctorDto = new CreateDoctorRequestFaker().Generate();
        
        var response = await client.PostAsJsonAsync(Urls.Doctor.Create, doctorDto);
        response.EnsureSuccessStatusCode();
        
        _stubDoctor = await response.Content.ReadFromJsonAsync<Doctor>();
    }

    public Task DisposeAsync()
    {
        return Task.CompletedTask;
    }
}
