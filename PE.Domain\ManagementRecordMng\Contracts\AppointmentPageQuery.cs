﻿﻿using PE.Domain.ManagementRecordMng.Entities;

namespace PE.Domain.ManagementRecordMng.Contracts;

/// <summary>
/// 预约分页查询条件
/// </summary>
public class AppointmentPageQuery
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 助理ID
    /// </summary>
    public long? AssistantId { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    public long? CustomerId { get; set; }

    /// <summary>
    /// 预约日期开始
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 预约日期结束
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public AppointmentStatus? Status { get; set; }
}
