﻿using PE.Common.Framework.ApiContext;
using PE.Common.Framework.Authentication;
using System.Security.Claims;

namespace PE.WebApi.Tests.Fixtures
{
    internal class JwtFixtures
    {
        static readonly JwtOptions _options = new()
        {
            Audience = "Health",
            Issuer = "Health",
            Expired = 3,
            SecretKey = "12345678901234567890123456789012"
        };

        static readonly JwtProvider _provider = new(_options);


        public static string GenerateToken4Admin()
        {
            return _provider.GenerateToken(GenerateClaims4Admin());
        }

        public static string GenerateToken4Doctor()
        {
            return _provider.GenerateToken(GenerateClaims4Doctor());
        }


        private static List<Claim> GenerateClaims4Admin()
        {
            return [
                new Claim(HealthClaimTypes.Id, "1"),
                //new Claim(HealthClaimTypes.Name, "admin"),
                new Claim(HealthClaimTypes.Role, AuthorizePolicies.Admin)
            ];
        }

        private static List<Claim> GenerateClaims4Doctor()
        {
            return [
                new Claim(HealthClaimTypes.Id, "1"),
                //new Claim(HealthClaimTypes.Name, "doctor"),
                new Claim(HealthClaimTypes.Role, AuthorizePolicies.Doctor)
            ];
        }
    }
}
