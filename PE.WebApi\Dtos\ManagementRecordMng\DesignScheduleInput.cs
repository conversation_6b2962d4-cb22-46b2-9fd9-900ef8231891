﻿namespace PE.WebApi.Dtos.ManagementRecordMng;

/// <summary>
/// 设计方案入参
/// </summary>
public class DesignScheduleInput
{
    /// <summary>
    /// 管理记录Id 
    /// </summary>
    public long RecordId { get; set; }

    /// <summary>
    /// 疗程Id
    /// </summary>
    public long CourseId { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    public long ExpertId { get; set; }

    /// <summary>
    /// 疗程明细
    /// </summary>
    //public List<ScheduleDto>? Schedules { get; set; }
    public List<DesignDetailInput> Details { get; set; } = [];


    ///// <summary>
    ///// 专家名称
    ///// </summary>
    //public string? ExpertName { get; set; }
}

public class DesignDetailInput
{
    /// <summary>
    /// 日程Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 日程Id
    /// </summary>
    public long ScheduleId { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int SerialNo { get; set; }

    ///// <summary>
    ///// 预计执行日期
    ///// </summary>
    //public DateTime ExpectedDate { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    public string? ItemRemark { get; set; }
}