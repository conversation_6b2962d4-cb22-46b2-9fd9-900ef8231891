﻿using PE.Domain.QuotationMng.Relations;

namespace PE.Domain.QuotationMng.Entities;

/// <summary>
/// 团检报价单
/// </summary>
[SugarTable("group_quotations", TableDescription = "团检报价单")]
[SugarIndex("unique_{table}_quotation_no", nameof(QuotationNo), OrderByType.Asc, true)]
public class Quotation : Entity, IAggregateRoot
{
    /// <summary>
    /// 报价单ID
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "报价单ID")]
    public long Id { get; set; }

    /// <summary>
    /// 团检单位ID
    /// </summary>
    /// </summary>
    /// <returns></returns>
    [SugarColumn(ColumnName = "company_id", ColumnDescription = "团检单位ID")]
    public int CompanyId { get; set; }

    /// <summary>
    /// 报价单号(唯一)
    /// </summary>
    [SugarColumn(ColumnName = "quotation_no", ColumnDescription = "报价单号(唯一)")]
    public string QuotationNo { get; set; }

    /// <summary>
    /// 状态：草稿/待审核/已审核
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态：0:草稿; 1:待审核, 2:已审核")]
    public QuotationStatus Status { get; set; }

    /// <summary>
    /// 创建人(业务员ID)
    /// </summary>
    [SugarColumn(ColumnName = "creator_id", ColumnDescription = "创建人(业务员ID)")]
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>
    [SugarColumn(ColumnName = "creator_name", ColumnDescription = "创建人名称")]
    public string CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "create_time", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 审核人ID
    /// </summary>
    [SugarColumn(ColumnName = "auditor_id", ColumnDescription = "审核人ID")]
    public int? AuditorId { get; set; }

    /// <summary>
    /// 审核人名称
    /// </summary>
    [SugarColumn(ColumnName = "auditor_name", ColumnDescription = "审核人名称")]
    public string? AuditorName { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(ColumnName = "audit_time", ColumnDescription = "审核时间")]
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 报价说明
    /// </summary>
    [SugarColumn(ColumnName = "remark", Length = 500, ColumnDescription = "报价说明")]
    public string? Remark { get; set; }


    /// <summary>
    /// 报价单明细
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IPackageOfQuotation Packages { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}

/// <summary>
/// 团体报价单状态
/// </summary>
public enum QuotationStatus
{
    /// <summary>
    /// 草稿
    /// </summary>
    Draft = 0,
    /// <summary>
    /// 待审核
    /// </summary>
    PendingApproval = 1,
    /// <summary>
    /// 已审核
    /// </summary>
    Approved = 2
}
