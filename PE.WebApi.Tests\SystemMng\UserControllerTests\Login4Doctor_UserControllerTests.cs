﻿using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos.DoctorMng;
using PE.WebApi.Dtos.SystemMng;

namespace PE.WebApi.Tests.SystemMng.UserControllerTests
{
    [Collection(nameof(DatabaseCollection))]
    public class Login4Doctor_UserControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly Faker<Doctor> _faker = new Faker<Doctor>()
            .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
            .RuleFor(x => x.Password, faker => faker.Internet.Password())
            .RuleFor(x => x.Name, faker => faker.Person.FullName)
            .RuleFor(x => x.RoleId, faker => 1)
        ;


        [Fact]
        public async Task Login4Doctor_返回登录信息_当用户存在且密码正确时()
        {
            var stub = _faker.Generate();
            var req = new CreateDoctorRequest
            {
                UserName = stub.UserName,
                Password = stub.Password,
                Name = stub.Name,
                //AutographUrl = stub.AutographUrl,
                //RoleId = stub.RoleId,
            };
            var client = _factory.CreateClient(JwtFixtures.GenerateToken4Admin());
            var response = await client.PostAsJsonAsync(Urls.Doctor.Create, req);
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var loginRequest = new LoginRequest() { UserName = stub.UserName, Password = stub.Password };

            client = _factory.CreateClient();
            var loginResponse = await client.PostAsJsonAsync(Urls.User.DoctorLogin, loginRequest);

            loginResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            var loginResult = await loginResponse.Content.ReadFromJsonAsync<LoginResult<DoctorDto>>();
            loginResult!.AccessToken.Should().NotBeNullOrEmpty();
            loginResult!.ExpiresIn.Should().BeGreaterThan(0);
            loginResult!.User.Id.Should().BeGreaterThan(0);
            loginResult!.User.Name.Should().Be(stub.Name);
        }

        [Fact]
        public async Task Login4Doctor_返回404_当用户不存在或密码错误时()
        {
            CreateDoctorRequest req = new() { UserName = "doctor", Password = "Asdf!2345", Name = "张三" };
            var client = _factory.CreateClient();

            var response = await client.PostAsJsonAsync(Urls.User.DoctorLogin, req);

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
            string msg = await response.Content.ReadAsStringAsync();
            msg.Should().Contain("用户不存在或密码错误");
        }


        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }
    }
}
