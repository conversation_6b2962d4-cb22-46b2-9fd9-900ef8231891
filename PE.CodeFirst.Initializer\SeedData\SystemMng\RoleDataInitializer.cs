﻿using PE.Domain.SystemMng.Entities;
using SqlSugar;

namespace PE.CodeFirst.Initializer.SeedData.SystemMng
{
    public class RoleDataInitializer : IDataInitializer
    {
        public void Init(SqlSugarClient db)
        {
            int count = db.Insertable(GenerateData()).ExecuteCommand();
            Console.WriteLine($" sys_role 初始化了 {count} 条数据");
        }

        public List<Role> GenerateData()
        {
            return [
                new Role
                {
                    Name = "管理员",
                    MenuIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]
                }
            ];
        }
    }
}
