using Autofac.Extensions.DependencyInjection;
using PE.Domain.FileMng.Entities;
using PE.WebApi.Extensions;
using PE.WebApi.Middlewares;
using PE.Common.Framework.Encryption;
using PE.Common.Utils;
using Microsoft.Extensions.FileProviders;
using Microsoft.OpenApi.Models;
using NLog.Web;
using System.Reflection;
using NewLife.Redis.Core;

namespace PE.WebApi
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            #region configure builder

            builder.Logging.ClearProviders();
            builder.Logging.AddNLog("NLog.config");
            builder.Services.AddNewLifeRedis();
            builder.Services.AddMiniProfiler();

            builder.Services.AddFluentValidationAutoValidation();

            builder.Services.AddSingleton<IEncryptor, EmptyEncryptor>();

            builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
            builder.Services.AddProblemDetails();

            builder.Services.AddControllers();
            builder.Services.AddEndpointsApiExplorer();

            builder.Services.AddSwaggerGen(c =>
            {
                var xmlPath = Path.Combine(AppContext.BaseDirectory,
                    $"{Assembly.GetExecutingAssembly().GetName().Name}.xml");
                c.IncludeXmlComments(xmlPath);

                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Health API", Version = "v1" });
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme.",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new List<string>()
                    }
                });
            });

            builder.Services.AddJwtAuthentication(builder.Configuration);
            builder.Services.AddJwtAuthorization();

            builder.Services.AddCors(options =>
                    options.AddPolicy("Cors",
                        cpBuilder =>
                        cpBuilder.SetIsOriginAllowed(t => true)
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials()
                    )
                );

            builder.Services.AddSqlSugar(builder.Configuration);

            builder.Services.AddAutoMapperProfiles();

            builder.Host.UseNLog();

            builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
                    .AddRepositories();



            #endregion

            var app = builder.Build();

            #region configure app

            FileUtil.CreateDirectoryIfNotExists(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, FileResource.RootPath));
            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, FileResource.RootPath)),
                RequestPath = $"/{FileResource.RootPath}"
            });

            app.UseMiniProfiler();
            app.UseMiddleware<MiniProfilerMiddleware>();

            //if (app.Environment.IsDevelopment())
            //{
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Health API V1");
            });
            //}

            app.UseMiddleware<LoggingMiddleware>();

            app.UseExceptionHandler();

            app.UseMiddleware<EncryptionMiddleware>();

            app.UseCors("Cors");

            app.UseHttpsRedirection();

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();

            #endregion

            app.Run();

        }
    }
}
