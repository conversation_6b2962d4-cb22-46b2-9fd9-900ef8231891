﻿using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Infrastructure.ManagementRecordMng.Relations;

public class DetailOfSchedule(HealthDbContext dbContext, CourseSchedule master)
    : BaseRelation<CourseSchedule, ScheduleDetail>(db<PERSON><PERSON><PERSON><PERSON>, master), IDetailOfSchedule
{
    public Task ClearAsync()
    {
        throw new NotImplementedException();
    }

    public async Task<bool> IsCompleteAsync()
    {
        return !await DbContext.ScheduleDetails.AsQueryable()
            .Where(x => x.ScheduleId == Master.Id && !x.IsComplete)
            .AnyAsync();
    }

}
