﻿
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Controllers.ManagementRecordMng;

namespace PE.WebApi.Dtos.ManagementRecordMng
{
    public class CreateCourseInput
    {
        //public long Id { get; set; }

        /// <summary>
        /// 管理记录Id
        /// </summary>
        public long ManagementRecordId { get; set; }

        /// <summary>
        /// 疗程序号
        /// </summary>
        public int SerialNo { get; set; }

        /// <summary>
        /// 健康管理助理ID
        /// </summary>
        public long AssistantId { get; set; }

        /// <summary>
        /// 健康管理助理姓名
        /// </summary>
        public string? AssistantName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 周期（x天/次）
        /// </summary>
        public int Cycle { get; set; }
    }

    public class CreateCourseOutput(CourseDto course, List<ScheduleDto> schedules)
    {
        public long Id { get; set; } = course.Id;

        /// <summary>
        /// 管理记录Id
        /// </summary>
        public long ManagementRecordId { get; set; } = course.ManagementRecordId;

        /// <summary>
        /// 疗程序号
        /// </summary>
        public int SerialNo { get; set; } = course.SerialNo;

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletionTime { get; set; } = course.CompletionTime;

        /// <summary>
        /// 状态（0：新建；1：进行中；2：完成）
        /// </summary>
        public int Status { get; set; } = course.Status;

        /// <summary>
        /// 周期(字符串，每日、隔天、住宿)
        /// </summary>
        public string? Cycle { get; set; } = course.Cycle;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; } = course.Remark;

        /// <summary>
        /// 客户评价
        /// </summary>
        public string? Reviews { get; set; } = course.Reviews;

        /// <summary>
        /// 日程表
        /// </summary>
        public List<ScheduleDto> Schedules { get; set; } = schedules;
    }
}
