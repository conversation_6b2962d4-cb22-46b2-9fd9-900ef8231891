﻿using System.Linq.Expressions;
using PE.Domain.Base;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.Domain.ManagementRecordMng.Roles;
using Moq;

public class PhysioTests
{
    private readonly Mock<IMrOfDoctor> _mockRecords = new();
    private readonly Mock<IAppointmentRepository> _mockAppointmentRepo = new();
    private readonly Doctor _doctor = new() { Id = 1, Name = "张三" };

    private Physio CreatePhysio() => new(_doctor, _mockRecords.Object, _mockAppointmentRepo.Object);

    [Fact]
    public async Task StartDetail_ShouldSetDetailAndUpdateAppointment()
    {
        // Arrange
        var detail = new ScheduleDetail { ScheduleId = 100 };
        var mockDetails = new Mock<IDetailOfSchedule>();
        var schedule = new CourseSchedule { Details = mockDetails.Object };
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Expression<Func<ManagementRecord, bool>>>()))
            .ReturnsAsync(new ManagementRecord { Courses = Mock.Of<ICourseOfManagementRecord>() });
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        var mockSchedules = new Mock<IScheduleOfCourse>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Expression<Func<Course, bool>>>()))
            .ReturnsAsync(new Course { Schedules = mockSchedules.Object });
        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Expression<Func<CourseSchedule, bool>>>()))
            .ReturnsAsync(schedule);

        // 让 GetScheduleAsync 能顺利返回
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Expression<Func<ManagementRecord, bool>>>()))
            .ReturnsAsync(new ManagementRecord { Courses = mockCourses.Object });

        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Expression<Func<CourseSchedule, bool>>>()))
            .ReturnsAsync(schedule);

        // 预约仓储
        var appointment = new Appointment { ScheduleId = 100 };
        _mockAppointmentRepo.Setup(r => r.GetSingleAsync(It.IsAny<Expression<Func<Appointment, bool>>>()))
            .ReturnsAsync(appointment);

        // Act
        var physio = CreatePhysio();
        await physio.StartDetail(1, 2, detail);

        // Assert
        Assert.Equal(_doctor.Id, detail.PhysioId);
        Assert.Equal(_doctor.Name, detail.PhysioName);
        Assert.NotNull(detail.BegingTime);
        _mockAppointmentRepo.Verify(r => r.UpdateAsync(appointment), Times.Once);
        mockDetails.Verify(d => d.UpdateAsync(detail), Times.Once);
    }

    /*
    [Fact]
    public async Task StartDetail_ShouldNotUpdateAppointment_WhenAppointmentNull()
    {
        // Arrange
        var detail = new ScheduleDetail { ScheduleId = 200 };
        var mockDetails = new Mock<IDetailOfSchedule>();
        var schedule = new CourseSchedule { Details = mockDetails.Object };
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        var mockSchedules = new Mock<IBaseRelation<ManagementRecord, CourseSchedule>>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Func<CourseOfManagementRecord, bool>>()))
            .ReturnsAsync(new CourseOfManagementRecord { Schedules = mockSchedules.Object });
        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Func<CourseSchedule, bool>>()))
            .ReturnsAsync(schedule);
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Func<ManagementRecord, bool>>()))
            .ReturnsAsync(new ManagementRecord { Courses = mockCourses.Object });
        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Func<CourseSchedule, bool>>()))
            .ReturnsAsync(schedule);

        _mockAppointmentRepo.Setup(r => r.GetSingleAsync(It.IsAny<Func<Appointment, bool>>()))
            .ReturnsAsync((Appointment?)null);

        // Act
        var physio = CreatePhysio();
        await physio.StartDetail(1, 2, detail);

        // Assert
        _mockAppointmentRepo.Verify(r => r.UpdateAsync(It.IsAny<Appointment>()), Times.Never);
        mockDetails.Verify(d => d.UpdateAsync(detail), Times.Once);
    }

    [Fact]
    public async Task CompleteDetail_ShouldSetDetailAndUpdateSchedule()
    {
        // Arrange
        var detail = new ScheduleDetail { ScheduleId = 300 };
        var mockDetails = new Mock<IDetailOfSchedule>();
        mockDetails.Setup(d => d.IsCompleteAsync()).ReturnsAsync(true);

        var schedule = new CourseSchedule { Details = mockDetails.Object };
        var mockSchedules = new Mock<IBaseRelation<CourseOfManagementRecord, CourseSchedule>>();
        mockSchedules.Setup(s => s.UpdateAsync(schedule)).Returns(Task.CompletedTask);

        var course = new CourseOfManagementRecord { Schedules = mockSchedules.Object };
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Expression<Func<CourseOfManagementRecord, bool>>>()))
            .ReturnsAsync(course);

        var mr = new ManagementRecord { Courses = mockCourses.Object };
        //_mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Func<ManagementRecord, bool>>()))
        //    .ReturnsAsync(mr);
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Expression<Func<ManagementRecord, bool>>>()))
            .ReturnsAsync(mr);

        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Expression<Func<CourseOfManagementRecord, bool>>>()))
            .ReturnsAsync(course);

        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Expression<Func<CourseSchedule, bool>>>()))
            .ReturnsAsync(schedule);

        // Act
        var physio = CreatePhysio();
        await physio.CompleteDetail(1, 2, detail);

        // Assert
        Assert.True(detail.IsComplete);
        Assert.NotNull(detail.EndTime);
        Assert.True(schedule.IsComplete);
        mockDetails.Verify(d => d.UpdateAsync(detail), Times.Once);
        mockSchedules.Verify(s => s.UpdateAsync(schedule), Times.Once);
    }

    [Fact]
    public async Task CompleteDetail_ShouldNotUpdateSchedule_WhenNotAllDetailsComplete()
    {
        // Arrange
        var detail = new ScheduleDetail { ScheduleId = 400 };
        var mockDetails = new Mock<IDetailOfSchedule>();
        mockDetails.Setup(d => d.IsCompleteAsync()).ReturnsAsync(false);

        var schedule = new CourseSchedule { Details = mockDetails.Object };
        var mockSchedules = new Mock<IBaseRelation<CourseOfManagementRecord, CourseSchedule>>();
        mockSchedules.Setup(s => s.UpdateAsync(schedule)).Returns(Task.CompletedTask);

        var course = new CourseOfManagementRecord { Schedules = mockSchedules.Object };
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Func<CourseOfManagementRecord, bool>>()))
            .ReturnsAsync(course);

        var mr = new ManagementRecord { Courses = mockCourses.Object };
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Func<ManagementRecord, bool>>()))
            .ReturnsAsync(mr);

        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Func<CourseOfManagementRecord, bool>>()))
            .ReturnsAsync(course);

        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Func<CourseSchedule, bool>>()))
            .ReturnsAsync(schedule);

        // Act
        var physio = CreatePhysio();
        await physio.CompleteDetail(1, 2, detail);

        // Assert
        Assert.True(detail.IsComplete);
        Assert.NotNull(detail.EndTime);
        Assert.False(schedule.IsComplete);
        mockDetails.Verify(d => d.UpdateAsync(detail), Times.Once);
        mockSchedules.Verify(s => s.UpdateAsync(schedule), Times.Never);
    }

    [Fact]
    public async Task GetScheduleAsync_ShouldThrow_WhenCourseSchedulesNull()
    {
        // Arrange
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Func<CourseOfManagementRecord, bool>>()))
            .ReturnsAsync(new CourseOfManagementRecord { Schedules = null });
        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Func<ManagementRecord, bool>>()))
            .ReturnsAsync(new ManagementRecord { Courses = mockCourses.Object });

        var physio = CreatePhysio();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            physio.GetType().GetMethod("GetScheduleAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)!
                .Invoke(physio, new object[] { 1L, 2L, 3L }) as Task
        );
    }

    [Fact]
    public async Task GetScheduleAsync_ShouldThrow_WhenScheduleDetailsNull()
    {
        // Arrange
        var mockSchedules = new Mock<IBaseRelation<CourseOfManagementRecord, CourseSchedule>>();
        mockSchedules.Setup(s => s.GetSingleAsync(It.IsAny<Func<CourseSchedule, bool>>()))
            .ReturnsAsync(new CourseSchedule { Details = null });

        var course = new CourseOfManagementRecord { Schedules = mockSchedules.Object };
        var mockCourses = new Mock<ICourseOfManagementRecord>();
        mockCourses.Setup(c => c.GetSingleAsync(It.IsAny<Func<CourseOfManagementRecord, bool>>()))
            .ReturnsAsync(course);

        _mockRecords.Setup(r => r.GetSingleAsync(It.IsAny<Func<ManagementRecord, bool>>()))
            .ReturnsAsync(new ManagementRecord { Courses = mockCourses.Object });

        var physio = CreatePhysio();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            physio.GetType().GetMethod("GetScheduleAsync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)!
                .Invoke(physio, new object[] { 1L, 2L, 3L }) as Task
        );
    }
    */
}
