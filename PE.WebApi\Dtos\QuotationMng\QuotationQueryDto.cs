using PE.Domain.QuotationMng.Entities;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 报价单查询DTO
/// </summary>
public class QuotationQueryDto
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// 关键字搜索（报价单号）
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 状态筛选
    /// </summary>
    public QuotationStatus? Status { get; set; }

    /// <summary>
    /// 团检客户ID
    /// </summary>
    public int? CompanyId { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    public long? CreatorId { get; set; }

    /// <summary>
    /// 创建开始时间
    /// </summary>
    public DateTime? CreateTimeStart { get; set; }

    /// <summary>
    /// 创建结束时间
    /// </summary>
    public DateTime? CreateTimeEnd { get; set; }
}
