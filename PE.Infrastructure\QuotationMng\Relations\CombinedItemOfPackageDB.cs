using System.Linq.Expressions;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.QuotationMng.Relations;

public class CombinedItemOfPackageDB(HealthDbContext dbContext, ExaminationPackage master)
    : BaseRelation<ExaminationPackage, ExaminationCombinedItem>(db<PERSON><PERSON>x<PERSON>, master), ICombinedItemOfPackage
{
    public async Task<List<ExaminationCombinedItem>> GetAllAsync()
    {
        return await DbContext.Db.Queryable<ExaminationCombinedItem>()
            .Where(x => x.PackageId == Master.Id)
            .OrderBy(x => x.Code)
            .ToListAsync();
    }

    public override async Task<List<ExaminationCombinedItem>> GetListAsync(Expression<Func<ExaminationCombinedItem, bool>>? expression = null)
    {
        var query = DbContext.Db.Queryable<ExaminationCombinedItem>()
            .Where(x => x.PackageId == Master.Id);

        if (expression != null)
        {
            query = query.Where(expression);
        }

        return await query.OrderBy(x => x.Code).ToListAsync();
    }

    public override async Task<ExaminationCombinedItem> GetSingleAsync(Expression<Func<ExaminationCombinedItem, bool>>? expression = null)
    {
        var item = await base.GetSingleAsync(expression) ?? throw new NotFoundException("检查项目组合明细未找到");
        return item;
    }

    public override async Task AddAsync(ExaminationCombinedItem item)
    {
        item.PackageId = Master.Id;
        item.Id = await DbContext.Db.Insertable(item).ExecuteReturnBigIdentityAsync();
    }

    public override async Task AddRangeAsync(List<ExaminationCombinedItem> items)
    {
        foreach (var item in items)
        {
            item.PackageId = Master.Id;
        }
        await DbContext.Db.Insertable(items).ExecuteCommandAsync();
    }
}
