using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Roles;

namespace PE.Domain.QuotationMng;

public interface IQuotationContext : IDomainContext
{
    /// <summary>
    /// 包含报价单的关系数据
    /// </summary>
    /// <param name="quotation">报价单实体</param>
    void Include(Quotation quotation);

    /// <summary>
    /// 包含检查套餐的关系数据
    /// </summary>
    /// <param name="package">检查套餐实体</param>
    void Include(ExaminationPackage package);

    /// <summary>
    /// 创建业务员角色
    /// </summary>
    /// <param name="doctor">医生实体</param>
    /// <returns>业务员角色</returns>
    Salesman AsSalesman(Doctor doctor);

    /// <summary>
    /// 创建审核员角色
    /// </summary>
    /// <param name="doctor">医生实体</param>
    /// <returns>审核员角色</returns>
    Approver AsApprover(Doctor doctor);
}
