﻿namespace PE.WebApi.Middlewares
{
    public class LoggingMiddleware
    {
        readonly RequestDelegate _next;
        readonly ILogger<LoggingMiddleware> _logger;

        public LoggingMiddleware(RequestDelegate next, ILogger<LoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }


        public async Task InvokeAsync(HttpContext context)
        {
            MemoryStream requestStream = new(), responseStream = new();
            Stream originalBodyStream = context.Response.Body;
            context.Response.Body = responseStream;
            await ReplaceRequestBodyAsync(context.Request, requestStream);

            await LogRequestAsync(context.Request, _logger);

            await _next(context);

            await LogResponseAsync(context.Response, _logger);

            await RestoreResponseBodyAsync(context.Response, originalBodyStream);
        }

        private static async Task LogRequestAsync(HttpRequest request, ILogger logger)
        {
            StreamReader requestReader = new(request.Body);
            string bodyContent = await requestReader.ReadToEndAsync();
            request.Body.Seek(0, SeekOrigin.Begin);

            logger.LogInformation(RequestLogTemplate,
                request.HttpContext.TraceIdentifier,
                request.Method,
                request.Path,
                request.QueryString,
                request.HttpContext.Connection.RemoteIpAddress,
                bodyContent);
        }

        private static async Task LogResponseAsync(HttpResponse response, ILogger logger)
        {
            if (response.Body.Length < 1)
                return;

            response.Body.Seek(0, SeekOrigin.Begin);
            string body = await new StreamReader(response.Body).ReadToEndAsync();
            response.Body.Seek(0, SeekOrigin.Begin);

            logger.LogInformation(ResponseLogTemplate,
                response.HttpContext.TraceIdentifier,
                response.StatusCode.ToString(),
                body);
        }

        private static async Task ReplaceRequestBodyAsync(HttpRequest request, MemoryStream requestStream)
        {
            await request.Body.CopyToAsync(requestStream);
            requestStream.Seek(0, SeekOrigin.Begin);
            request.Body = requestStream;
        }

        private static async Task RestoreResponseBodyAsync(HttpResponse response, Stream originalStream)
        {
            await response.Body.CopyToAsync(originalStream);
            response.Body = originalStream;
        }



        const string RequestLogTemplate = @"
Request: 
    TraceId: {traceId},
    Method: {method},
    Path: {path},
    Query: {query}
    From: {from},
    Body: {body}
";

        const string ResponseLogTemplate = @"
Response:
    TraceId: {traceId},
    StatusCode: {statusCode},
    Body: {body}
";
    }
}
