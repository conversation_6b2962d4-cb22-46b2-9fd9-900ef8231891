using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;
using PE.Domain.QuotationMng.Roles;
using Moq;

namespace PE.Domain.Tests.QuotationMng.Roles;

public class SalesmanTests
{
    private readonly Mock<IQuotationOfSalesman> _mockQuotations = new();
    private readonly Doctor _doctor = new() { Id = 1, Name = "张三" };

    private Salesman CreateSalesman() => new(_doctor, _mockQuotations.Object);

    [Fact]
    public async Task CreateQuotationAsync_ShouldSetPropertiesAndAddToRepository()
    {
        // Arrange
        var quotation = new Quotation
        {
            CompanyId = 100,
            Remark = "测试报价单"
        };

        // Act
        var salesman = CreateSalesman();
        await salesman.CreateQuotationAsync(quotation);

        // Assert
        Assert.Equal(QuotationStatus.Draft, quotation.Status);
        Assert.Equal(_doctor.Id, quotation.CreatorId);
        Assert.Equal(_doctor.Name, quotation.CreatorName);
        Assert.NotNull(quotation.CreateTime);
        Assert.StartsWith("QT", quotation.QuotationNo);
        _mockQuotations.Verify(q => q.AddAsync(quotation), Times.Once);
    }

    [Fact]
    public async Task SubmitForApprovalAsync_ShouldUpdateStatusToPendingApproval()
    {
        // Arrange
        var quotation = new Quotation
        {
            Id = 1,
            Status = QuotationStatus.Draft,
            CreatorId = _doctor.Id
        };

        _mockQuotations.Setup(q => q.GetSingleAsync(It.IsAny<Expression<Func<Quotation, bool>>>()))
            .ReturnsAsync(quotation);

        // Act
        var salesman = CreateSalesman();
        await salesman.SubmitForApprovalAsync(1);

        // Assert
        Assert.Equal(QuotationStatus.PendingApproval, quotation.Status);
        _mockQuotations.Verify(q => q.UpdateAsync(quotation), Times.Once);
    }

    [Fact]
    public async Task SubmitForApprovalAsync_ShouldNotUpdateStatus_WhenNotDraft()
    {
        // Arrange
        var quotation = new Quotation
        {
            Id = 1,
            Status = QuotationStatus.Approved,
            CreatorId = _doctor.Id
        };

        _mockQuotations.Setup(q => q.GetSingleAsync(It.IsAny<Expression<Func<Quotation, bool>>>()))
            .ReturnsAsync(quotation);

        // Act
        var salesman = CreateSalesman();
        await salesman.SubmitForApprovalAsync(1);

        // Assert
        Assert.Equal(QuotationStatus.Approved, quotation.Status);
        _mockQuotations.Verify(q => q.UpdateAsync(It.IsAny<Quotation>()), Times.Never);
    }

    [Fact]
    public async Task GetMyQuotationsAsync_ShouldReturnQuotationsList()
    {
        // Arrange
        var expectedQuotations = new List<Quotation>
        {
            new() { Id = 1, CreatorId = _doctor.Id },
            new() { Id = 2, CreatorId = _doctor.Id }
        };

        _mockQuotations.Setup(q => q.GetMyQuotationsAsync())
            .ReturnsAsync(expectedQuotations);

        // Act
        var salesman = CreateSalesman();
        var result = await salesman.GetMyQuotationsAsync();

        // Assert
        Assert.Equal(expectedQuotations, result);
        _mockQuotations.Verify(q => q.GetMyQuotationsAsync(), Times.Once);
    }

    [Fact]
    public async Task GetDraftQuotationsAsync_ShouldReturnDraftQuotationsList()
    {
        // Arrange
        var expectedQuotations = new List<Quotation>
        {
            new() { Id = 1, Status = QuotationStatus.Draft, CreatorId = _doctor.Id },
            new() { Id = 2, Status = QuotationStatus.Draft, CreatorId = _doctor.Id }
        };

        _mockQuotations.Setup(q => q.GetDraftQuotationsAsync())
            .ReturnsAsync(expectedQuotations);

        // Act
        var salesman = CreateSalesman();
        var result = await salesman.GetDraftQuotationsAsync();

        // Assert
        Assert.Equal(expectedQuotations, result);
        _mockQuotations.Verify(q => q.GetDraftQuotationsAsync(), Times.Once);
    }
}
