﻿using PE.Domain.FollowUpMng;
using PE.Domain.FollowUpMng.Roles;

namespace PE.Infrastructure.FollowUpMng
{
    public class FollowUpContext //: IFollowUpContext
    {
        HealthDbContext DbContext { get; }

        public FollowUpContext(HealthDbContext dbContext)
        {
            DbContext = dbContext;
        }

        //public IFollowUpDoctor AsFollowUpDoctor(User user)
        //{
        //    return new FollowUpDoctor(user);
        //}
    }
}
