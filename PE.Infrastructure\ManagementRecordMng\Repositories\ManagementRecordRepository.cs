﻿using System.Linq.Expressions;
using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.Infrastructure.ManagementRecordMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.ManagementRecordMng.Repositories;

public class ManagementRecordRepository(HealthDbContext dbContext) : BaseRepository<ManagementRecord>(dbContext), IManagementRecordRepository
{
    public async Task<(List<ManagementRecord>, int totalNumber)> GetPageAsync(RecordPageQuery query)
    {
        RefAsync<int> count = 0;
        List<ManagementRecord> list = await DbContext.ManagementRecords.AsQueryable()
            .WhereIF(query.CustomerId.HasValue, r => r.CustomerId == query.CustomerId)
            .WhereIF(query.ExpertId.HasValue, r => r.ExpertId == query.ExpertId)
            .WhereIF(query.AssistantId.HasValue, r => r.AssistantId == query.AssistantId)
            .WhereIF(query.Status != 0, r => r.Status == query.Status)
            .ToPageListAsync(query.PageIndex, query.PageSize, count);

        return (list, count.Value);
    }

    public override async Task<ManagementRecord> GetSingleAsync(Expression<Func<ManagementRecord, bool>>? expression = null)
    {
        var record = await base.GetSingleAsync(expression) ?? throw new NotFoundException($"管理记录未找到");
        record.Courses = new CourseOfManagementRecordDB(DbContext, record);
        record.Questionnaire = new QuestionnaireOfManagementRecord(DbContext, record);

        return record;
    }
}
