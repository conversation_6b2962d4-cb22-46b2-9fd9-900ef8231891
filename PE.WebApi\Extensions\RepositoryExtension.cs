﻿using Autofac;
using PE.Infrastructure;

namespace PE.WebApi.Extensions
{
    public static class RepositoryExtension
    {
        public static IHostBuilder AddRepositories(this IHostBuilder hostBuilder)
        {
            hostBuilder.ConfigureContainer<ContainerBuilder>(builder =>
            {
                builder.RegisterAssemblyTypes(typeof(HealthDbContext).Assembly)
                    .AsImplementedInterfaces();
            });

            return hostBuilder;
        }
    }
}
