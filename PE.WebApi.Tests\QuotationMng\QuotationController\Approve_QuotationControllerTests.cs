using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Tests.Fakers.QuotationMng;
using Humanizer;

namespace PE.WebApi.Tests.QuotationMng.QuotationController;

[Collection(nameof(DatabaseCollection))]
public class Approve_QuotationControllerTests(CustomWebApplicationFactory factory) : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly CreateQuotationDtoFaker _faker = new();
    private Doctor? _stubSalesman;
    private Doctor? _stubApprover;
    private QuotationDto? _stubQuotation;

    [Fact]
    public async Task Approve_返回成功_当报价单为待审核状态时()
    {
        // Arrange
        var client = _factory.CreateClient();
        
        // 先提交审核
        var submitUrl = Urls.Quotation.Submit.FormatWith(_stubQuotation!.Id) + $"?salesmanId={_stubSalesman!.Id}";
        var submitResponse = await client.PostAsync(submitUrl, null);
        submitResponse.EnsureSuccessStatusCode();

        // 然后审核通过
        var approveUrl = Urls.Quotation.Approve.FormatWith(_stubQuotation.Id) + $"?approverId={_stubApprover!.Id}";

        // Act
        var response = await client.PostAsync(approveUrl, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // 验证状态已更改
        var getUrl = Urls.Quotation.Get.FormatWith(_stubQuotation.Id);
        var getResponse = await client.GetAsync(getUrl);
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var updatedQuotation = await getResponse.Content.ReadFromJsonAsync<QuotationDto>();
        updatedQuotation!.Status.Should().Be(Domain.QuotationMng.Entities.QuotationStatus.Approved);
        updatedQuotation.AuditorId.Should().Be((int)_stubApprover.Id);
        updatedQuotation.AuditorName.Should().Be(_stubApprover.Name);
        updatedQuotation.AuditTime.Should().NotBeNull();
    }

    [Fact]
    public async Task Approve_返回404_当审核员不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        
        // 先提交审核
        var submitUrl = Urls.Quotation.Submit.FormatWith(_stubQuotation!.Id) + $"?salesmanId={_stubSalesman!.Id}";
        var submitResponse = await client.PostAsync(submitUrl, null);
        submitResponse.EnsureSuccessStatusCode();

        var nonExistentApproverId = 99999;
        var approveUrl = Urls.Quotation.Approve.FormatWith(_stubQuotation.Id) + $"?approverId={nonExistentApproverId}";

        // Act
        var response = await client.PostAsync(approveUrl, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task Approve_返回400_当报价单不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var nonExistentQuotationId = 99999;
        var approveUrl = Urls.Quotation.Approve.FormatWith(nonExistentQuotationId) + $"?approverId={_stubApprover!.Id}";

        // Act
        var response = await client.PostAsync(approveUrl, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    public async Task InitializeAsync()
    {
        var client = _factory.CreateClient();

        // 创建测试用的业务员数据
        var salesmanDto = new CreateDoctorRequestFaker().Generate();
        var salesmanResponse = await client.PostAsJsonAsync(Urls.Doctor.Create, salesmanDto);
        salesmanResponse.EnsureSuccessStatusCode();
        _stubSalesman = await salesmanResponse.Content.ReadFromJsonAsync<Doctor>();

        // 创建测试用的审核员数据
        var approverDto = new CreateDoctorRequestFaker().Generate();
        var approverResponse = await client.PostAsJsonAsync(Urls.Doctor.Create, approverDto);
        approverResponse.EnsureSuccessStatusCode();
        _stubApprover = await approverResponse.Content.ReadFromJsonAsync<Doctor>();

        // 创建测试用的报价单数据
        var quotationDto = _faker.Generate();
        var quotationUrl = $"{Urls.Quotation.Create}?salesmanId={_stubSalesman!.Id}";
        var quotationResponse = await client.PostAsJsonAsync(quotationUrl, quotationDto);
        quotationResponse.EnsureSuccessStatusCode();
        _stubQuotation = await quotationResponse.Content.ReadFromJsonAsync<QuotationDto>();
    }

    public Task DisposeAsync()
    {
        return Task.CompletedTask;
    }
}
