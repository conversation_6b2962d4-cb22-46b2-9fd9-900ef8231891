﻿#pragma warning disable CS8618

namespace PE.WebApi.Dtos.ManagementRecordMng;

#region input

/// <summary>
/// 日程表
/// </summary>
public class UpdateScheduleListInput
{
    /// <summary>
    /// 疗程Id
    /// </summary>
    public long CourseId { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    public long ExpertId { get; set; }

    /// <summary>
    /// 日程表
    /// </summary>
    public List<UpdateScheduleInput> Schedules { get; set; }
}

/// <summary>
/// 日程
/// </summary>
public class UpdateScheduleInput
{
    /// <summary>
    /// 序号
    /// </summary>
    public int SerialNo { get; set; }

    /// <summary>
    /// 预计执行日期
    /// </summary>
    public DateTime ExpectedDate { get; set; }

    /// <summary>
    /// 详情
    /// </summary>
    public List<UpdateDetailInput> Details { get; set; }
}

/// <summary>
/// 日程明细
/// </summary>
public class UpdateDetailInput
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    public string ItemRemark { get; set; }
}

#endregion

#region output

/// <summary>
/// 日程表
/// </summary>
public class CreateScheduleListOutput
{
    /// <summary>
    /// 日程表
    /// </summary>
    public List<UpdateScheduleInput> Schedules { get; set; }
}

/// <summary>
/// 日程
/// </summary>
public class CreateScheduleOutput
{
    /// <summary>
    /// 日程Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int SerialNo { get; set; }

    /// <summary>
    /// 预计执行时间
    /// </summary>
    public DateTime ExpectedDate { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    public List<CreateDetailOutput> Details { get; set; }
}

/// <summary>
/// 日程明细
/// </summary>
public class CreateDetailOutput
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    public string ItemRemark { get; set; }
}




/// <summary>
/// 日程表
/// </summary>
public class ScheduleListDto
{
    /// <summary>
    /// 日程表
    /// </summary>
    public List<UpdateScheduleInput> Schedules { get; set; }
}

/// <summary>
/// 日程
/// </summary>
public class ScheduleDto
{
    /// <summary>
    /// 日程Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int SerialNo { get; set; }

    /// <summary>
    /// 预计执行时间
    /// </summary>
    public DateTime ExpectedDate { get; set; }

    /// <summary>
    /// 明细
    /// </summary>
    public List<DetailDto> Details { get; set; }
}

/// <summary>
/// 日程明细
/// </summary>
public class DetailDto
{
    /// <summary>
    /// 明细ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 项目ID
    /// </summary>
    public long ItemId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ItemName { get; set; }

    /// <summary>
    /// 项目备注
    /// </summary>
    public string ItemRemark { get; set; }
}
#endregion