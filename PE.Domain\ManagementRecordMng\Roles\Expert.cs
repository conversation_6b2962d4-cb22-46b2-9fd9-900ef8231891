﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Domain.ManagementRecordMng.Roles;

/// <summary>
/// 专家
/// </summary>
public class Expert(Doctor doctor, IMrOfDoctor records)
{
    public Doctor Doctor { get; } = doctor;
    public IMrOfDoctor Records { get; } = records;


    /// <summary>
    /// 制定疗程
    /// </summary>
    /// <param name="recordId"></param>
    /// <param name="courseId"></param>
    /// <param name="allDetails"></param>
    public async Task DesignCourseAsync(long recordId, long courseId, List<ScheduleDetail> allDetails)
    {
        var mr = await Records.GetSingleAsync(r => r.Id == recordId);
        var course = await mr.Courses!.GetSingleAsync(c => c.Id == courseId);

        foreach (var d in allDetails)
        {
            d.CourseId = courseId;
        }

        await course.Schedules!.ClearDetailsAsync();
        await course.Schedules!.AddDetailsAsync(allDetails);
    }
}
