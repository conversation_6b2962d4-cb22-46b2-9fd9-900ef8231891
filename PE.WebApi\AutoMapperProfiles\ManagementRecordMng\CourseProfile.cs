﻿using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Controllers.ManagementRecordMng;
using PE.WebApi.Dtos.ManagementRecordMng;

namespace PE.WebApi.AutoMapperProfiles.ManagementRecordMng
{
    public class CourseProfile : Profile
    {
        public CourseProfile()
        {
            CreateMap<CreateCourseInput, Course>();
            CreateMap<Course, CreateCourseOutput>();
            CreateMap<Course, CourseDto>().ReverseMap();
        }
    }
}
