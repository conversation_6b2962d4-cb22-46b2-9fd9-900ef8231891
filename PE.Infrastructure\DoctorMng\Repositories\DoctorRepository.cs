﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng;
using PE.Domain.SystemMng;

namespace PE.Infrastructure.DoctorMng.Repositories
{
    public class DoctorRepository(HealthDbContext dbContext, IUserContext userContext, IManagementContext managementContext)
        : BaseRepository<Doctor>(dbContext), IDoctorRepository
    {
        private IUserContext _userContext = userContext;
        private IManagementContext _managementContext = managementContext;

        public IManagementContext InManagementContext()
        {
            return _managementContext;
        }

        public IUserContext InUserContext()
        {
            return _userContext;
        }
    }
}
