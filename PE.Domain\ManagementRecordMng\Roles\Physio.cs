﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Domain.ManagementRecordMng.Repositories;

namespace PE.Domain.ManagementRecordMng.Roles;

/// <summary>
/// 理疗师
/// </summary>
public class Physio(Doctor doctor, IMrOfDoctor records, IAppointmentRepository appointmentRepository)
{
    public Doctor Doctor { get; } = doctor;

    public IMrOfDoctor Records { get; } = records;

    public IAppointmentRepository AppointmentRepository { get; } = appointmentRepository;


    /// <summary>
    /// 开始
    /// </summary>
    /// <param name="recordId"></param>
    /// <param name="courseId"></param>
    /// <param name="detail"></param>
    /// <returns></returns>
    public async Task StartDetail(long recordId, long courseId, ScheduleDetail detail)
    {
        var schedule = await GetScheduleAsync(recordId, courseId, detail.ScheduleId);

        detail.BegingTime = DateTime.Now;
        detail.PhysioId = Doctor.Id;
        detail.PhysioName = Doctor.Name;

        await schedule.Details!.UpdateAsync(detail);

        // 更新预约状态为已报到（状态值2）
        var appointment = await AppointmentRepository.GetSingleAsync(a => a.ScheduleId == detail.ScheduleId);
        if (appointment != null)
        {
            appointment.Status = AppointmentStatus.CheckedIn;
            await AppointmentRepository.UpdateAsync(appointment);
        }
    }


    /// <summary>
    /// 完成
    /// </summary>
    /// <param name="recordId"></param>
    /// <param name="courseId"></param>
    /// <param name="detail"></param>
    /// <returns></returns>
    public async Task CompleteDetail(long recordId, long courseId, ScheduleDetail detail)
    {
        var mr = await Records.GetSingleAsync(r => r.Id == recordId);
        var course = await mr.Courses!.GetSingleAsync(c => c.Id == courseId);
        var schedule = await course.Schedules!.GetSingleAsync(s => s.Id == detail.ScheduleId);

        detail.EndTime = DateTime.Now;
        detail.IsComplete = true;

        await schedule.Details!.UpdateAsync(detail);

        if (await schedule.Details.IsCompleteAsync())
        {
            schedule.IsComplete = true;

            await course.Schedules.UpdateAsync(schedule);
        }
    }


    /// <summary>
    /// 获取日程
    /// </summary>
    /// <param name="recordId"></param>
    /// <param name="courseId"></param>
    /// <param name="scheduleId"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    private async Task<CourseSchedule> GetScheduleAsync(long recordId, long courseId, long scheduleId)
    {
        var mr = await Records.GetSingleAsync(r => r.Id == recordId);
        var course = await mr.Courses!.GetSingleAsync(c => c.Id == courseId);

        // 确保course.Schedules已经初始化
        if (course.Schedules == null)
        {
            throw new InvalidOperationException("疗程的日程关系未初始化，请检查CourseOfManagementRecordDB.GetSingleAsync方法的实现");
        }

        var schedule = await course.Schedules.GetSingleAsync(s => s.Id == scheduleId);

        // 确保schedule.Details已经初始化
        if (schedule.Details == null)
        {
            throw new InvalidOperationException("日程的明细关系未初始化，请检查ScheduleOfCourseDB.GetSingleAsync方法的实现");
        }

        return schedule;
    }
}
