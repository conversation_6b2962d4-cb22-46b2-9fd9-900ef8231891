﻿using AutoMapper;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Dtos;
using PE.WebApi.Dtos.ManagementRecordMng;
using PE.WebApi.Middlewares;
using PE.Common.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace PE.WebApi.Controllers.ManagementRecordMng;

public class AppointmentController(IMapper mapper, IDoctorRepository doctorRepository) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;

    /// <summary>
    /// 创建预约
    /// </summary>
    /// <param name="dto">预约信息</param>
    /// <returns>创建的预约信息</returns>
    [HttpPost("prebook")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<PreBookDto>))]
    public async Task<ActionResult<AppointmentDto>> PreBook([FromBody] PreBookDto dto)
    {
        try
        {
            // 获取助理
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == dto.AssistantId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{dto.AssistantId}的助理未找到");
            }

            // 创建预约
            var appointment = _mapper.Map<Appointment>(dto);
            var assistant = _doctorRepository.InManagementContext().AsAssistant(doctor);
            var createdAppointment = await assistant.CreateAppointmentAsync(appointment);

            // 返回创建的预约信息
            return Ok(_mapper.Map<AppointmentDto>(createdAppointment));
        }
        catch (DomainException ex)
        {
            return Problem400Result(ex.Message);
        }
    }


    /// <summary>
    /// 分页查询预约记录
    /// </summary>
    /// <param name="dto">查询条件</param>
    /// <returns>预约记录列表</returns>
    [HttpGet("page")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<Page4AppointmentDto>))]
    public async Task<ActionResult<PageResultDto<AppointmentDto>>> GetPage([FromBody] Page4AppointmentDto dto)
    {
        try
        {
            if (dto.PageIndex < 1 || dto.PageSize < 1)
                return Problem400Result("分页参数错误");

            // 将 DTO 转换为领域层的查询对象
            var query = _mapper.Map<AppointmentPageQuery>(dto);

            // 获取助理
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == 1); // 这里使用默认ID，实际应该使用当前登录用户的ID
            var assistant = _doctorRepository.InManagementContext().AsAssistant(doctor);

            // 使用领域层的查询对象查询预约记录
            (List<Appointment> pageItems, int totalCount) = await assistant.Appointments.GetPagedListAsync(query);

            // 返回结果
            var items = _mapper.Map<List<AppointmentDto>>(pageItems);
            return Ok(new PageResultDto<AppointmentDto>(dto.PageIndex, dto.PageSize, totalCount, items));
        }
        catch (DomainException ex)
        {
            return Problem400Result(ex.Message);
        }
    }
}
