using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Repositories;
using PE.Domain.QuotationMng.Roles;
using PE.Infrastructure.QuotationMng.Relations;
using PE.Infrastructure.QuotationMng.Repositories;

namespace PE.Infrastructure.QuotationMng;

public class QuotationContext(HealthDbContext dbContext, IQuotationRepository quotationRepository) : IQuotationContext
{
    public HealthDbContext DbContext { get; } = dbContext;
    public IQuotationRepository QuotationRepository { get; } = quotationRepository;

    public Salesman As<PERSON>alesman(Doctor doctor)
    {
        return new Salesman(doctor, new QuotationOfSalesmanDB(DbContext, doctor));
    }

    public Approver AsApprover(Doctor doctor)
    {
        return new Approver(doctor, new QuotationOfApproverDB(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, doctor));
    }

    public void Include(Quotation quotation)
    {
        quotation.Packages = new PackageOfQuotationDB(DbContext, quotation);
    }

    public void Include(ExaminationPackage package)
    {
        package.CombinedItems = new CombinedItemOfPackageDB(DbContext, package);
    }
}
