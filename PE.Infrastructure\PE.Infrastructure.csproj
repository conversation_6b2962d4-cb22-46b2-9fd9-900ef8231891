﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\PE.Domain\PE.Domain.csproj" />
    <ProjectReference Include="..\PE.Infrastructure.Core\PE.Infrastructure.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Encodings.Web" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CustomerMng\Relations\" />
    <Folder Include="DoctorMng\Relations\" />
  </ItemGroup>

</Project>
