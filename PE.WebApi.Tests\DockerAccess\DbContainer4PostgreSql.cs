﻿using DotNet.Testcontainers.Builders;
using SqlSugar;
using Testcontainers.PostgreSql;

namespace PE.WebApi.Tests.DockerAccess;

internal class DbContainer4PostgreSql : IDockerContainer
{
    readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithImage("postgres:latest")
        .WithEnvironment("POSTGRES_USER", "postgres")
        .WithEnvironment("POSTGRES_PASSWORD", "postgres")
        .WithEnvironment("POSTGRES_DB", "postgres")
        .WithPortBinding(5555, 5432)
        .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(5432))
        .Build();


    public string GetConnectionString()
    {
        return _dbContainer.GetConnectionString();
    }

    public DbType GetDbType()
    {
        return DbType.PostgreSQL;
    }


    public Task StartAsync()
    {
        return _dbContainer.StartAsync();
    }

    public ValueTask DisposeAsync()
    {
        return _dbContainer.DisposeAsync();
    }
}
