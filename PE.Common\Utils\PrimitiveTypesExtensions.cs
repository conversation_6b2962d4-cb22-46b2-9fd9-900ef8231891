﻿using System.Collections;
using System.Collections.ObjectModel;

namespace PE.Common.Utils
{
    public static class PrimitiveTypesExtensions
    {
        public static bool IsNullOrEmpty(this string? source)
        {
            return string.IsNullOrEmpty(source);
        }

        public static bool IsNotNullOrEmpty(this string? source)
        {
            return !string.IsNullOrEmpty(source);
        }

        public static bool IsNullOrEmpty<T>(this IEnumerable<T>? list)
        {
            return list is null || !list.Any();
        }
    }
}
