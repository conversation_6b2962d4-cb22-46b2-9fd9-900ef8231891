﻿//#define Test

using PE.Domain.DoctorMng.Entities;
using PE.Common.Framework.Encryption;
using SqlSugar;

namespace PE.CodeFirst.Initializer.SeedData.DoctorMng
{
    public class DoctorDataInitializer : IDataInitializer
    {
        public void Init(SqlSugarClient db)
        {
            try
            {
                int count = db.Insertable(GenerateData()).ExecuteCommand();
                Console.WriteLine($" sys_doctors 初始化了 {count} 条数据");
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("duplicate key value violates unique constraint"))
                {
                    Console.WriteLine(" sys_doctors 已存在种子数据，跳过初始化");
                }
                else 
                {
                    Console.WriteLine($" sys_doctors 初始化发生错误");
                }
            }
        }

        public List<Doctor> GenerateData()
        {
            return [
                new Doctor
                {
                    UserName = "admin",
                    Password = Md5Encryptor.GetMD5Hash("12345678", Md5Encryptor.DefaultSalt),
                    Name = "admin",
                    RoleId = 1
                },

//#if Test
//                new Doctor
//                {
//                    UserName = "testMan4",
//                    Password = Md5Encryptor.GetMD5Hash("123456", Md5Encryptor.DefaultSalt),
//                    Name = "man4",
//                    RoleId = 1
//                },
//                new Doctor
//                {
//                    UserName = "doctor1",
//                    Password = Md5Encryptor.GetMD5Hash("123456", Md5Encryptor.DefaultSalt),
//                    Name = "xingming1",
//                    RoleId = 1
//                },
//                new Doctor
//                {
//                    UserName = "doctor2",
//                    Password = Md5Encryptor.GetMD5Hash("123456", Md5Encryptor.DefaultSalt),
//                    Name = "xingming2",
//                    RoleId = 1
//                },
//                new Doctor
//                {
//                    UserName = "doctor3",
//                    Password = Md5Encryptor.GetMD5Hash("123456", Md5Encryptor.DefaultSalt),
//                    Name = "xingming3",
//                    RoleId = 1
//                },
//#endif

            ];
        }
    }
}
