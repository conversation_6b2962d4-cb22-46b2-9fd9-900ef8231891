﻿using PE.Domain.FollowUpMng.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace PE.WebApi.Controllers.FollowUpMng
{
    public class FollowUpPlanController : BaseController
    {
        IFollowUpPlanRepository _planRepository;
        ILogger<FollowUpPlanController> _logger;

        public FollowUpPlanController(IFollowUpPlanRepository planRepository, ILogger<FollowUpPlanController> logger)
        {
            _planRepository = planRepository;
            _logger = logger;
        }


        ///// <summary>
        ///// 创建计划
        ///// </summary>
        ///// <param name="input"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[Authorize]
        //public async Task<IActionResult> CreatePlan(AddPlanInput input)
        //{
        //    await Task.CompletedTask;
        //    _logger.LogInformation("创建计划" + input.ToString());
        //    //throw new NotImplementedException();
        //    return Ok(await Task.FromResult(input));
        //}


        ///// <summary>
        ///// 变更计划状态
        ///// </summary>
        ///// <param name="id"></param>
        ///// <param name="input"></param>
        ///// <returns></returns>
        //[HttpPut("{id}/status")]
        //public async Task<IActionResult> ChangeStatus(long id, [FromBody] ChangePlanStatusInput input)
        //{
        //    var plan = await _planRepository.GetSingleAsync(p => p.Id == id);
        //    plan.Status = input.Status;
        //    await _planRepository.UpdateAsync(plan);

        //    return Ok();
        //}
    }
}
