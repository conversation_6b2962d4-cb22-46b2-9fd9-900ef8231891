﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Infrastructure.ManagementRecordMng.Relations
{
    public class AppointmentOfAssistantDB(HealthDbConte<PERSON><PERSON> db<PERSON><PERSON><PERSON><PERSON>, Doctor master)
        : BaseRelation<Doctor, Appointment>(db<PERSON><PERSON><PERSON><PERSON>, master), IAppointmentOfAssistant
    {
        /// <summary>
        /// 根据查询条件获取预约记录列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>预约记录列表和总记录数</returns>
        public async Task<(List<Appointment> list, int totalCount)> GetPagedListAsync(AppointmentPageQuery query)
        {
            // 使用 RefAsync 来接收总记录数
            RefAsync<int> totalCount = 0;

            // 使用 SqlSugar 的 WhereIF 和 ToPageListAsync 方法直接在数据库层面进行分页查询
            var list = await DbContext.Db.Queryable<Appointment>()
                // 助理ID过滤
                .WhereIF(query.AssistantId.HasValue, a => a.AssistantId == query.AssistantId)
                // 客户ID过滤
                .WhereIF(query.CustomerId.HasValue, a => a.CustomerId == query.CustomerId)
                // 日期范围过滤
                .WhereIF(query.StartDate.HasValue, a => a.AppointmentDate >= query.StartDate!.Value.Date)
                .WhereIF(query.EndDate.HasValue, a => a.AppointmentDate <= query.EndDate!.Value.Date.AddDays(1).AddSeconds(-1))
                // 状态过滤
                .WhereIF(query.Status.HasValue, a => a.Status == query.Status!.Value)
                // 按创建时间降序排序
                .OrderByDescending(a => a.CreationTime)
                // 执行分页查询
                .ToPageListAsync(query.PageIndex, query.PageSize, totalCount);

            return (list, totalCount.Value);
        }
    }
}
