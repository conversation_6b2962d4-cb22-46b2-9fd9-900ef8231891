﻿using System.Numerics;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.Domain.ManagementRecordMng.Roles;
using PE.Infrastructure.ManagementRecordMng.Relations;
using PE.Infrastructure.ManagementRecordMng.Repositories;

namespace PE.Infrastructure.ManagementRecordMng;

public class ManagementContext(HealthDbContext dbContext, IAppointmentRepository appointmentRepository) : IManagementContext
{
    public HealthDbContext DbContext { get; } = dbContext;
    public IAppointmentRepository AppointmentRepository { get; } = appointmentRepository;

    public Assistant AsAssistant(Doctor dr)
    {
        return new Assistant(dr, new MrOfDoctorDB(DbContext, dr), new AppointmentOfAssistantDB(DbContext, dr));
    }

    public Expert AsExpert(Doctor dr)
    {
        return new Expert(dr, new MrOfDoctorDB(DbContext, dr));
    }

    public Physio AsPhysio(Doctor dr)
    {
        return new Physio(dr, new MrOfDoctorDB(DbContext, dr), AppointmentRepository);
    }

    public void Include(ManagementRecord record)
    {
        record.Courses = new CourseOfManagementRecordDB(DbContext, record);
        record.Questionnaire = new QuestionnaireOfManagementRecord(DbContext, record);
    }

    public void Include(Course course)
    {
        course.Schedules = new ScheduleOfCourseDB(DbContext, course);
    }
}
