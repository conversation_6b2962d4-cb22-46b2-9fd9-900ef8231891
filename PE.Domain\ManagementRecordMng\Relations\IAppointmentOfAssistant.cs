﻿﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;

namespace PE.Domain.ManagementRecordMng.Relations
{
    /// <summary>
    /// 助理的预约关系
    /// </summary>
    public interface IAppointmentOfAssistant : IBaseRelation<Doctor, Appointment>
    {
        /// <summary>
        /// 根据查询条件获取预约记录列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>预约记录列表和总记录数</returns>
        Task<(List<Appointment> list, int totalCount)> GetPagedListAsync(AppointmentPageQuery query);
    }
}
