﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Roles;

namespace PE.Domain.ManagementRecordMng;

public interface IManagementContext : IDomainContext
{
    public void Include(ManagementRecord record);

    public void Include(Course course);


    public Assistant As<PERSON>sistant(Doctor dr);

    public Expert AsExpert(Doctor dr);

    public Physio <PERSON>Physio(Doctor dr);
}