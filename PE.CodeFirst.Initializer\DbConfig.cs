﻿//#define USE_SQLSERVER

using SqlSugar;

namespace PE.CodeFirst.Initializer
{
    internal class DbConfig
    {
#if USE_SQLSERVER
        public static readonly string ConnectionString = "Data Source=.;Initial Catalog=Health;User ID=sa;Password=*********;";
        public static readonly DbType DbType = DbType.SqlServer;
#else
        public static readonly string ConnectionString = "Host=************:5433;Database=TCMH;Username=********;Password=********;";
        public static readonly DbType DbType = DbType.PostgreSQL;
#endif
    }
}
