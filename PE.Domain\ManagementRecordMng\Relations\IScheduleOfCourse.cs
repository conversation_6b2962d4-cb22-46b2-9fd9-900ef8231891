﻿using PE.Domain.ManagementRecordMng.Entities;

namespace PE.Domain.ManagementRecordMng.Relations;

/// <summary>
/// 疗程的日程
/// </summary>
public interface IScheduleOfCourse : IBaseRelation<Course, CourseSchedule>
{
    Task DesignScheduleAsync(CourseSchedule schedule);

    Task ClearDetailsAsync();

    Task AddDetailsAsync(List<ScheduleDetail> allDetails);

    Task<List<ScheduleDetail>> GetDetailsAsync();
}
