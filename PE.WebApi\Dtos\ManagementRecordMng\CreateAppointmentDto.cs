﻿using FluentValidation;
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Dtos.CutomerMng;

namespace PE.WebApi.Dtos.ManagementRecordMng
{
    public class AppointmentDto
    {
        public long Id { get; set; }

        /// <summary>
        /// 日程Id
        /// </summary>
        public long ScheduleId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public long? CustomerId { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 预约日期
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// 上下午
        /// </summary>
        public int MeridiemType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public AppointmentStatus? Status { get; set; }

        ///// <summary>
        ///// 预约记录创建人Id
        ///// </summary>
        //public long CreatorId { get; set; }

        ///// <summary>
        ///// 预约记录创建人姓名
        ///// </summary>
        //public string? CreatorName { get; set; }

        /// <summary>
        /// 助理ID
        /// </summary>
        public long? AssistantId { get; set; }

        /// <summary>
        /// 助理姓名
        /// </summary>
        public string? AssistantName { get; set; }

        ///// <summary>
        ///// 预约记录创建时间
        ///// </summary>
        //public DateTime CreationTime { get; set; }
    }


    public class PreBookDto
    {
        /// <summary>
        /// 日程Id
        /// </summary>
        public long ScheduleId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public long? CustomerId { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 预约日期
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// 上下午
        /// </summary>
        public int MeridiemType { get; set; }

        /// <summary>
        /// 助理ID
        /// </summary>
        public long AssistantId { get; set; }

        /// <summary>
        /// 助理姓名
        /// </summary>
        public string? AssistantName { get; set; }
    }


    public class PreBookDtoValidator : AbstractValidator<PreBookDto>
    {
        public PreBookDtoValidator()
        {
            RuleFor(x => x.ScheduleId).NotEmpty().WithMessage("日程ID不能为空");
            RuleFor(x => x.AppointmentDate).NotEmpty().WithMessage("预约日期不能为空");
            RuleFor(x => x.AssistantId).GreaterThan(0).WithMessage("助理ID不能为空");
        }
    }
}
