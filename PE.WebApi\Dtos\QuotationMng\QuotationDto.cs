using PE.Domain.QuotationMng.Entities;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 报价单DTO
/// </summary>
public class QuotationDto
{
    /// <summary>
    /// 报价单ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 团检客户ID
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// 报价单号
    /// </summary>
    public string QuotationNo { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public QuotationStatus Status { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>
    public string CreatorName { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 审核人ID
    /// </summary>
    public int? AuditorId { get; set; }

    /// <summary>
    /// 审核人名称
    /// </summary>
    public string? AuditorName { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 报价说明
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 报价单明细
    /// </summary>
    public List<ExaminationPackageDto>? Packages { get; set; }
}
