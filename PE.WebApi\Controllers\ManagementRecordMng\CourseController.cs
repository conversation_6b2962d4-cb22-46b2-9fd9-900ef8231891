﻿using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.Infrastructure.Core;
using PE.WebApi.Dtos.ManagementRecordMng;
using PE.Common.Exceptions;

namespace PE.WebApi.Controllers.ManagementRecordMng;

/// <summary>
/// 疗程管理
/// </summary>
public class CourseController(IMapper mapper, IDoctorRepository doctorRepository, IManagementRecordRepository recordRepository) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;
    private readonly IManagementRecordRepository _recordRepository = recordRepository;


    /// <summary>
    /// 获取疗程列表
    /// </summary>
    /// <param name="managementRecordId">管理记录ID</param>
    /// <returns></returns> 
    [HttpGet("getCourseList")]
    public async Task<ActionResult<List<CourseDto>>> GetCourseList([FromQuery] long managementRecordId)
    {
        ManagementRecord record = await _recordRepository.GetSingleAsync(r => r.Id == managementRecordId);
        List<Course> courses = await record.Courses!.GetListAsync();

        return Ok(_mapper.Map<List<CourseDto>>(courses));
    }


    /// <summary>
    /// 创建疗程
    /// * 生成空日程表
    /// </summary>
    /// <param name="dto">疗程信息</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("create")]
    //[ServiceFilter(typeof(UnitOfWorkFilter))]
    [UnitOfWork]
    public async Task<ActionResult<CreateCourseOutput>> Create([FromBody] CreateCourseInput dto)
    {
        var courseInput = _mapper.Map<Course>(dto);

        var user = await _doctorRepository.GetSingleAsync(d => d.Id == dto.AssistantId);
        var assistant = _doctorRepository.InManagementContext().AsAssistant(user);

        try
        {
            var (course, schedules) = await assistant.CreateCourseAsync(courseInput);

            return new CreateCourseOutput(
                _mapper.Map<CourseDto>(course),
                _mapper.Map<List<ScheduleDto>>(schedules)
            );
        }
        catch (DomainException ex)
        {
            return Problem400Result(ex.Message);
        }
    }


    /// <summary>
    /// 设计方案
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("designSchedules")]
    public async Task<ActionResult> Design([FromBody] DesignScheduleInput dto)
    {
        var dr = await _doctorRepository.GetSingleAsync(d => d.Id == dto.ExpertId);
        var expert = _doctorRepository.InManagementContext().AsExpert(dr);

        List<ScheduleDetail> allDetails = _mapper.Map<List<ScheduleDetail>>(dto.Details);

        await expert.DesignCourseAsync(dto.RecordId, dto.CourseId, allDetails);

        return Ok();
    }


    /// <summary>
    /// 更新疗程（未实现）
    /// </summary>
    /// <param name="dto">疗程信息</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("update")]
    public Task<ActionResult> Update([FromBody] UpdateCourseInput dto)
    {
        throw new NotImplementedException();
    }


    /// <summary>
    /// 更新日程列表（未实现）
    /// </summary>
    /// <param name="dto">日程列表</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpPost("updateScheduleList")]
    public Task<ActionResult> UpdateScheduleList([FromBody] UpdateScheduleListInput dto)
    {
        throw new NotImplementedException();
    }


    /// <summary>
    /// 获取日程明细列表
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpGet("getScheduleDetailList")]
    public async Task<ActionResult<List<ScheduleDetailDto>>> GetScheduleDetailList([FromQuery] GetScheduleListInput dto)
    {
        var record = await _recordRepository.GetSingleAsync(r => r.Id == dto.RecordId);
        var course = await record.Courses!.GetSingleAsync(c => c.Id == dto.CourseId);
        var details = await course.Schedules!.GetDetailsAsync();

        return Ok(_mapper.Map<List<ScheduleDetailDto>>(details));
    }
}
