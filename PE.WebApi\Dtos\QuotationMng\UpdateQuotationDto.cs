namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 更新报价单DTO
/// </summary>
public class UpdateQuotationDto
{
    /// <summary>
    /// 报价单ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 团检单位ID
    /// </summary>
    public int CompanyId { get; set; }

    /// <summary>
    /// 报价说明
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 报价单明细
    /// </summary>
    public List<UpdateExaminationPackageDto>? Packages { get; set; }
}
