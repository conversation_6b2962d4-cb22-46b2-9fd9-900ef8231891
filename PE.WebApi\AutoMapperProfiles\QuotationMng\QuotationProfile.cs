using AutoMapper;
using PE.Domain.QuotationMng.Entities;
using PE.WebApi.Dtos.QuotationMng;

namespace PE.WebApi.AutoMapperProfiles.QuotationMng;

/// <summary>
/// 报价单AutoMapper配置
/// </summary>
public class QuotationProfile : Profile
{
    public QuotationProfile()
    {
        // 报价单映射
        CreateMap<Quotation, QuotationDto>()
            .ForMember(dest => dest.Packages, opt => opt.Ignore()); // 需要手动处理关联数据

        CreateMap<CreateQuotationDto, Quotation>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.QuotationNo, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.CreatorId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatorName, opt => opt.Ignore())
            .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
            .ForMember(dest => dest.AuditorId, opt => opt.Ignore())
            .ForMember(dest => dest.AuditorName, opt => opt.Ignore())
            .ForMember(dest => dest.AuditTime, opt => opt.Ignore())
            .ForMember(dest => dest.Packages, opt => opt.Ignore());

        CreateMap<UpdateQuotationDto, Quotation>()
            .ForMember(dest => dest.QuotationNo, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.CreatorId, opt => opt.Ignore())
            .ForMember(dest => dest.CreatorName, opt => opt.Ignore())
            .ForMember(dest => dest.CreateTime, opt => opt.Ignore())
            .ForMember(dest => dest.AuditorId, opt => opt.Ignore())
            .ForMember(dest => dest.AuditorName, opt => opt.Ignore())
            .ForMember(dest => dest.AuditTime, opt => opt.Ignore())
            .ForMember(dest => dest.Packages, opt => opt.Ignore());

        // 检查套餐映射
        CreateMap<ExaminationPackage, ExaminationPackageDto>()
            .ForMember(dest => dest.CombinedItems, opt => opt.Ignore()); // 需要手动处理关联数据

        CreateMap<CreateExaminationPackageDto, ExaminationPackage>()
            .ForMember(dest => dest.QuotationId, opt => opt.Ignore())
            .ForMember(dest => dest.CombinedItems, opt => opt.Ignore());

        CreateMap<UpdateExaminationPackageDto, ExaminationPackage>()
            .ForMember(dest => dest.QuotationId, opt => opt.Ignore())
            .ForMember(dest => dest.CombinedItems, opt => opt.Ignore());

        // 检查项目组合明细映射
        CreateMap<ExaminationCombinedItem, ExaminationCombinedItemDto>();

        CreateMap<CreateExaminationCombinedItemDto, ExaminationCombinedItem>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.PackageId, opt => opt.Ignore());

        CreateMap<UpdateExaminationCombinedItemDto, ExaminationCombinedItem>()
            .ForMember(dest => dest.PackageId, opt => opt.Ignore());
    }
}
