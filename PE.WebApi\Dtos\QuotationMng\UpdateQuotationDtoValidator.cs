using FluentValidation;

namespace PE.WebApi.Dtos.QuotationMng;

/// <summary>
/// 更新报价单DTO验证器
/// </summary>
public class UpdateQuotationDtoValidator : AbstractValidator<UpdateQuotationDto>
{
    public UpdateQuotationDtoValidator()
    {
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("报价单ID必须大于0");

        RuleFor(x => x.CompanyId)
            .GreaterThan(0)
            .WithMessage("团检客户ID必须大于0");

        RuleFor(x => x.Remark)
            .MaximumLength(500)
            .WithMessage("报价说明不能超过500个字符");

        RuleForEach(x => x.Packages)
            .SetValidator(new UpdateExaminationPackageDtoValidator());
    }
}
