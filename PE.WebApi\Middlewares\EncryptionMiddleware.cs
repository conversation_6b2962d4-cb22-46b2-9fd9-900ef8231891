﻿using PE.Common.Framework.Encryption;
using System.Text;

namespace PE.WebApi.Middlewares
{
    public class EncryptionMiddleware(RequestDelegate next, IEncryptor encryptor)
    {
        readonly RequestDelegate _next = next;
        readonly IEncryptor _encryptor = encryptor;


        public async Task Invoke(HttpContext context)
        {
            MemoryStream requestStream = new(), responseStream = new();
            Stream originalBodyStream = context.Response.Body;
            context.Response.Body = responseStream;
            await ReplaceRequestBodyAsync(context.Request, requestStream);

            await DecryptAsync(context.Request, _encryptor);

            await _next(context);

            await EncryptAsync(context.Response, _encryptor);

            await RestoreResponseBodyAsync(context.Response, originalBodyStream);
        }


        private static async Task DecryptAsync(HttpRequest request, IEncryptor encryptor)
        {
            StreamReader requestReader = new(request.Body);
            string bodyContent = await requestReader.ReadToEndAsync();
            request.Body.Seek(0, SeekOrigin.Begin);

            bodyContent = encryptor.Decrypt(bodyContent);
            MemoryStream requestBodyStream = new(Encoding.UTF8.GetBytes(bodyContent));
            await requestBodyStream.CopyToAsync(request.Body);
            request.Body.Seek(0, SeekOrigin.Begin);
        }

        private static async Task EncryptAsync(HttpResponse response, IEncryptor encryptor)
        {
            if (response.Body.Length < 1)
                return;

            response.Body.Seek(0, SeekOrigin.Begin);
            string bodyContent = await new StreamReader(response.Body).ReadToEndAsync();
            bodyContent = encryptor.Encrypt(bodyContent);

            MemoryStream responseBodyStream = new(Encoding.UTF8.GetBytes(bodyContent));
            response.Body = responseBodyStream;
            response.Body.Seek(0, SeekOrigin.Begin);
        }

        private static async Task RestoreResponseBodyAsync(HttpResponse response, Stream originalStream)
        {
            await response.Body.CopyToAsync(originalStream);
            response.Body = originalStream;
        }

        private static async Task ReplaceRequestBodyAsync(HttpRequest request, MemoryStream requestStream)
        {
            await request.Body.CopyToAsync(requestStream);
            requestStream.Seek(0, SeekOrigin.Begin);
            request.Body = requestStream;
        }
    }
}
