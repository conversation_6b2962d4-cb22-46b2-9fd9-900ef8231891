﻿using PE.Domain.Abstractions;
using SqlSugar;

namespace PE.CodeFirst.Initializer
{
    public class DataCleaner(string connecetionString, DbType dbType)
    {
        public string ConnecetionString { get; } = connecetionString;
        public DbType DbType { get; } = dbType;


        public async Task CleanAsync<T>()
            where T : Entity, new()
        {
            SqlSugarClient db = DbClientFactory.GetClient(ConnecetionString, DbType);
            await db.Deleteable<T>().ExecuteCommandAsync();
        }
    }
}
