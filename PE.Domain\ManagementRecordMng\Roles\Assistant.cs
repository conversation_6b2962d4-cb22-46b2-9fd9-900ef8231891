﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Common.Exceptions;

namespace PE.Domain.ManagementRecordMng.Roles;

/// <summary>
/// 助理
/// </summary>
public class Assistant(Doctor doctor, IMrOfDoctor records, IAppointmentOfAssistant appointments)
{
    public Doctor Doctor { get; } = doctor;

    public IMrOfDoctor Records { get; } = records;

    public IAppointmentOfAssistant Appointments { get; } = appointments;


    /// <summary>
    /// 创建管理记录
    /// </summary>
    /// <param name="record"></param>
    /// <returns></returns>
    public async Task CreateManagementRecordAsync(ManagementRecord record)
    {
        record.CreationTime = DateTime.Now;
        record.Id = await Records.AddReturnBigIdentityAsync(record);
    }


    /// <summary>
    /// 获取管理记录
    /// </summary>
    /// <returns></returns>
    public async Task<List<ManagementRecord>> GetRecords()
    {
        return await Records.GetListAsync(r => r.AssistantId == Doctor.Id);
    }


    /// <summary>
    /// 创建个人信息问卷
    /// </summary>
    /// <param name="questionnaire"></param>
    /// <returns></returns>
    public async Task CreateQuestionnaireAsync(PersonalInformationQuestionnaire questionnaire)
    {
        var record = await Records.GetSingleAsync(r => r.Id == questionnaire.ManagementRecordId);
        questionnaire.Id = await record.Questionnaire!.AddReturnBigIdentityAsync(questionnaire);
    }


    /// <summary>
    /// 更新问卷
    /// </summary>
    /// <param name="questionnaire"></param>
    /// <returns></returns>
    public async Task UpdateQuestionnaireAsync(PersonalInformationQuestionnaire questionnaire)
    {
        var record = await Records.GetSingleAsync(r => r.Id == questionnaire.ManagementRecordId);
        await record.Questionnaire!.UpdateAsync(questionnaire);
    }


    /// <summary>
    /// 创建疗程
    /// </summary>
    /// <param name="course"></param>
    /// <returns></returns>
    public async Task<(Course, List<CourseSchedule>)> CreateCourseAsync(Course course)
    {
        var record = await Records.GetSingleAsync(x => x.Id == course.ManagementRecordId);

        if (await record.Courses!.HasInProgressAsync())
            throw new DomainException("已有新开或进行中的疗程");

        await record.Courses!.AddAsync(course);

        List<CourseSchedule> schedules = await CreateSchedulesAsync(course);

        return (course, schedules);
    }


    /// <summary>
    /// 创建10条日程
    /// </summary>
    /// <param name="course"></param>
    /// <returns></returns>
    private async Task<List<CourseSchedule>> CreateSchedulesAsync(Course course)
    {
        List<CourseSchedule> schedules = [];
        for (int i = 0; i < 10; i++)
        {
            var s = new CourseSchedule
            {
                CourseId = course.Id,
                SerialNo = i + 1,
                ExpectedDate = DateTime.Now.AddDays(i + (i * course.Cycle!.Value)).Date,
            };
            s.Id = await course.Schedules!.AddReturnBigIdentityAsync(s);
            schedules.Add(s);
        }

        return await Task.FromResult(schedules);
    }


    /// <summary>
    /// 创建预约
    /// </summary>
    /// <param name="appointment">预约信息</param>
    /// <returns>创建的预约信息</returns>
    public async Task<Appointment> CreateAppointmentAsync(Appointment appointment)
    {
        // 设置创建人信息
        appointment.CreatorId = Doctor.Id;
        appointment.CreatorName = Doctor.Name;
        appointment.Status = AppointmentStatus.Booked; // 默认状态为已预约

        // 添加预约记录
        appointment.Id = await Appointments.AddReturnBigIdentityAsync(appointment);

        return appointment;
    }
}
