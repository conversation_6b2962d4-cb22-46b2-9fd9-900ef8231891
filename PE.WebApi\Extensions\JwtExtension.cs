﻿using PE.WebApi.OptionsSetup;
using PE.Common.Framework.ApiContext;
using PE.Common.Framework.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace PE.WebApi.Extensions
{
    public static class JwtExtension
    {
        public static void AddJwtAuthentication(this IServiceCollection services, ConfigurationManager configuration)
        {
            services.ConfigureOptions<JwtOptionsSetup>();

            var jwtOptions = configuration.GetSection(JwtOptionsSetup.SectionName).Get<JwtOptions>();
            services.AddScoped<IJwtProvider>(p => new JwtProvider(jwtOptions!));

            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = jwtOptions!.Issuer,
                    ValidAudience = jwtOptions!.Audience,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtOptions!.SecretKey))
                };
            });
        }


        public static void AddJwtAuthorization(this IServiceCollection services)
        {
            services.AddAuthorizationBuilder()
                .AddPolicy(AuthorizePolicies.Admin, policy =>
                {
                    policy.RequireClaim(HealthClaimTypes.Role, AuthorizePolicies.Admin);
                })
                .AddPolicy(AuthorizePolicies.Doctor, policy =>
                {
                    policy.RequireClaim(HealthClaimTypes.Role, AuthorizePolicies.Doctor);
                });
        }
    }
}
