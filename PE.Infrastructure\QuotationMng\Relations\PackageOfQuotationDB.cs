using System.Linq.Expressions;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.QuotationMng.Relations;

public class PackageOfQuotationDB(HealthDbContext dbContext, Quotation master)
    : BaseRelation<Quotation, ExaminationPackage>(db<PERSON>ontext, master), IPackageOfQuotation
{
    public async Task<List<ExaminationPackage>> GetAllAsync()
    {
        return await DbContext.Db.Queryable<ExaminationPackage>()
            .Where(x => x.QuotationId == Master.Id)
            .OrderBy(x => x.PackageCode)
            .ToListAsync();
    }

    public override async Task<List<ExaminationPackage>> GetListAsync(Expression<Func<ExaminationPackage, bool>>? expression = null)
    {
        var query = DbContext.Db.Queryable<ExaminationPackage>()
            .Where(x => x.QuotationId == Master.Id);

        if (expression != null)
        {
            query = query.Where(expression);
        }

        var packages = await query.OrderBy(x => x.PackageCode).ToListAsync();

        // 为每个套餐设置CombinedItems关系
        foreach (var package in packages)
        {
            package.CombinedItems = new CombinedItemOfPackageDB(DbContext, package);
        }

        return packages;
    }

    public override async Task<ExaminationPackage> GetSingleAsync(Expression<Func<ExaminationPackage, bool>>? expression = null)
    {
        var package = await base.GetSingleAsync(expression) ?? throw new NotFoundException("检查套餐未找到");
        package.CombinedItems = new CombinedItemOfPackageDB(DbContext, package);

        return package;
    }

    public override async Task AddAsync(ExaminationPackage package)
    {
        package.QuotationId = Master.Id;
        await DbContext.Db.Insertable(package).ExecuteCommandAsync();
        package.CombinedItems = new CombinedItemOfPackageDB(DbContext, package);
    }

    public override async Task AddRangeAsync(List<ExaminationPackage> packages)
    {
        foreach (var package in packages)
        {
            package.QuotationId = Master.Id;
        }
        await DbContext.Db.Insertable(packages).ExecuteCommandAsync();
        
        foreach (var package in packages)
        {
            package.CombinedItems = new CombinedItemOfPackageDB(DbContext, package);
        }
    }
}
