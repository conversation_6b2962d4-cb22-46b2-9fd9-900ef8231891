﻿using PE.Domain.CustomerMng.Entities;
using PE.Domain.CustomerMng.Repositories;
using PE.WebApi.Dtos;
using PE.WebApi.Dtos.CutomerMng;
using PE.WebApi.Middlewares;
using Npgsql;

namespace PE.WebApi.Controllers.CustomerMng;

public class CustomerController(ICustomerRepository customerRepository, IMapper mapper)
    : BaseController
{
    private readonly ICustomerRepository _customerRepository = customerRepository;
    private readonly IMapper _mapper = mapper;


    /// <summary>
    /// 获取客户
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<CustomerDto>> Get([FromQuery] long id)
    {
        var customer = await _customerRepository.GetSingleAsync(x => x.Id == id);
        if (customer is null)
            return Problem404Result();

        return Ok(_mapper.Map<CustomerDto>(customer));
    }


    /// <summary>
    /// 分页获取客户列表
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<ActionResult<PageResultDto<CustomerDto>>> GetPage([FromQuery] GetCustomerPageInput dto)
    {
        if (dto.PageIndex < 1 || dto.PageSize < 1)
            return Problem400Result("分页参数错误");

        var (list, totalCount) = await _customerRepository.GetPageAsync(dto.PageIndex, dto.PageSize,
            c => string.IsNullOrEmpty(dto.Keyword) || c.Name.Contains(dto.Keyword) || c.PhoneNo.Contains(dto.Keyword)
        );

        return Ok(new PageResultDto<Customer>(dto.PageIndex, dto.PageSize, totalCount, list!));
    }


    /// <summary>
    /// 创建客户
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("create")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CustomerDto>))]
    public async Task<ActionResult<CustomerDto>> Create([FromBody] CustomerDto dto)
    {
        var customer = _mapper.Map<Customer>(dto);
        try
        {
            customer.Id = await _customerRepository.AddReturnBigIdentityAsync(customer);
        }
        catch (NpgsqlException ex)
        {
            if (ex.SqlState == "23505")
                return Problem400Result(ex.Message);

            return Problem400Result("位置异常");
        }

        return Ok(await Task.FromResult(_mapper.Map<CustomerDto>(customer)));
    }


    /// <summary>
    /// 更新客户信息
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("update")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CustomerDto>))]
    public async Task<ActionResult> Update([FromBody] CustomerDto dto)
    {
        var customer = _mapper.Map<Customer>(dto);
        await _customerRepository.UpdateAsync(customer);

        return Ok();
    }


    ///// <summary>
    ///// excel导入
    ///// </summary>
    ///// <param name="dto"></param>
    ///// <returns></returns>
    //[HttpPost("importByExcel")]
    //public async Task<ActionResult> ImportByExcel([FromBody] ImportByExcelDto dto)
    //{
    //    var customers = dto.ValidateReturnValid(_mapper, out string err);
    //    if (!err.IsNullOrEmpty())
    //        return Problem400Result(err);

    //    await _customerRepository.AddRangeAsync(customers);

    //    return Ok();
    //}
}
