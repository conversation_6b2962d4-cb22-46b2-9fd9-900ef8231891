﻿namespace PE.Domain.QuotationMng.Entities;

/// <summary>
/// 团检项目组合明细
/// </summary>
[SugarTable("group_examination_combined_items", TableDescription = "团检项目组合明细")]
public class ExaminationCombinedItem : Entity
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 套餐ID
    /// </summary>
    [SugarColumn(ColumnName = "package_id", ColumnDescription = "套餐ID")]
    public string PackageId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "项目名称")]
    public string Name { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "项目编码")]
    public string Code { get; set; }

    /// <summary>
    /// 基础单价
    /// </summary>
    [SugarColumn(ColumnName = "base_price", ColumnDescription = "基础单价")]
    public decimal BasePrice { get; set; }

    /// <summary>
    /// 项目类别(如内科/影像科)
    /// </summary>
    [SugarColumn(ColumnName = "category", ColumnDescription = "项目类别(如内科/影像科)")]
    public string Category { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    [SugarColumn(ColumnName = "description", ColumnDescription = "项目描述")]
    public string Description { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
