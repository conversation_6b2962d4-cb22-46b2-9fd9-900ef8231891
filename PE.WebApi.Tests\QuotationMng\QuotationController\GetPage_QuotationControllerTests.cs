using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Tests.Fakers.QuotationMng;

namespace PE.WebApi.Tests.QuotationMng.QuotationController;

[Collection(nameof(DatabaseCollection))]
public class GetPage_QuotationControllerTests(CustomWebApplicationFactory factory) : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly CreateQuotationDtoFaker _faker = new();
    private Doctor? _stubDoctor;
    private readonly List<QuotationDto> _stubQuotations = new();

    [Fact]
    public async Task GetPage_返回分页结果_当参数有效时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var query = new QuotationQueryDto
        {
            PageIndex = 1,
            PageSize = 10
        };
        var url = $"{Urls.Quotation.GetPage}?PageIndex={query.PageIndex}&PageSize={query.PageSize}";

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<PageResultDto<QuotationDto>>();
        result.Should().NotBeNull();
        result!.PageIndex.Should().Be(query.PageIndex);
        result.PageSize.Should().Be(query.PageSize);
        result.Items.Should().NotBeNull();
        result.Items.Count.Should().BeGreaterThan(0);
        result.TotalCount.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetPage_返回筛选结果_当使用状态筛选时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var query = new QuotationQueryDto
        {
            PageIndex = 1,
            PageSize = 10,
            Status = Domain.QuotationMng.Entities.QuotationStatus.Draft
        };
        var url = $"{Urls.Quotation.GetPage}?PageIndex={query.PageIndex}&PageSize={query.PageSize}&Status={query.Status}";

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<PageResultDto<QuotationDto>>();
        result.Should().NotBeNull();
        result!.Items.Should().NotBeNull();
        result.Items.Should().OnlyContain(q => q.Status == Domain.QuotationMng.Entities.QuotationStatus.Draft);
    }

    [Fact]
    public async Task GetPage_返回400_当分页参数无效时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var url = $"{Urls.Quotation.GetPage}?PageIndex=0&PageSize=0";

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GetPage_返回筛选结果_当使用关键字搜索时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var targetQuotation = _stubQuotations.First();
        var keyword = targetQuotation.QuotationNo.Substring(0, 5); // 使用报价单号的前5位作为关键字
        var url = $"{Urls.Quotation.GetPage}?PageIndex=1&PageSize=10&Keyword={keyword}";

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<PageResultDto<QuotationDto>>();
        result.Should().NotBeNull();
        result!.Items.Should().NotBeNull();
        result.Items.Should().OnlyContain(q => q.QuotationNo.Contains(keyword));
    }

    public async Task InitializeAsync()
    {
        var client = _factory.CreateClient();

        // 创建测试用的医生数据
        var doctorDto = new CreateDoctorRequestFaker().Generate();
        var doctorResponse = await client.PostAsJsonAsync(Urls.Doctor.Create, doctorDto);
        doctorResponse.EnsureSuccessStatusCode();
        _stubDoctor = await doctorResponse.Content.ReadFromJsonAsync<Doctor>();

        // 创建多个测试用的报价单数据
        for (int i = 0; i < 5; i++)
        {
            var quotationDto = _faker.Generate();
            var quotationUrl = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";
            var quotationResponse = await client.PostAsJsonAsync(quotationUrl, quotationDto);
            quotationResponse.EnsureSuccessStatusCode();
            var createdQuotation = await quotationResponse.Content.ReadFromJsonAsync<QuotationDto>();
            _stubQuotations.Add(createdQuotation!);
        }
    }

    public Task DisposeAsync()
    {
        return Task.CompletedTask;
    }
}
