﻿using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos.DoctorMng;

namespace PE.WebApi.Tests.DoctorMng.DoctorController
{
    [Collection(nameof(DatabaseCollection))]
    public class GetList_DoctorControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly Faker<CreateDoctorRequest> _faker = new Faker<CreateDoctorRequest>()
            .RuleFor(x => x.Name, faker => faker.Name.FullName())
            .RuleFor(x => x.UserName, faker => faker.Internet.UserName())
            .RuleFor(x => x.Password, faker => faker.Internet.Password())
            .RuleFor(x => x.AutographUrl, faker => faker.Internet.Url())
            .RuleFor(x => x.RoleId, faker => faker.Random.Number(1, 10))
        ;


        [Fact]
        public async Task GetList_返回非空列表_当数据存在时()
        {
            var client = _factory.CreateClient(JwtFixtures.GenerateToken4Admin());
            for (int i = 0; i < 10; i++)
            {
                var d = _faker.Generate();
                await client.PostAsJsonAsync(Urls.Doctor.Create, d);
            }

            var doctors = await client.GetFromJsonAsync<List<DoctorDto>>(Urls.Doctor.List);

            doctors!.Count.Should().Be(10);

            await TestDataCleaner.CleanAsync<Doctor>();
        }


        [Fact]
        public async Task GetList_返回空列表_当数据不存在时()
        {
            var client = _factory.CreateClient();

            var doctors = await client.GetFromJsonAsync<List<Doctor>>(Urls.Doctor.List);

            doctors!.Count.Should().Be(0);
        }



        public async Task InitializeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Doctor>();
        }
    }
}
