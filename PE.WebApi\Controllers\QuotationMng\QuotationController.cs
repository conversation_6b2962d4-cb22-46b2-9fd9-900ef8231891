using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.QuotationMng;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Repositories;
using PE.Infrastructure.Core;
using PE.WebApi.Dtos;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Middlewares;

namespace PE.WebApi.Controllers.QuotationMng;

/// <summary>
/// 报价单控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class QuotationController(
    IQuotationRepository quotationRepository,
    IQuotationContext quotationContext,
    IDoctorRepository doctorRepository,
    IMapper mapper) : BaseController
{
    private readonly IQuotationRepository _quotationRepository = quotationRepository;
    private readonly IQuotationContext _quotationContext = quotationContext;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;
    private readonly IMapper _mapper = mapper;

    /// <summary>
    /// 获取报价单详情
    /// </summary>
    /// <param name="id">报价单ID</param>
    /// <returns>报价单详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<QuotationDto>> Get(long id)
    {
        //try
        //{
            var quotation = await _quotationRepository.GetSingleAsync(q => q.Id == id);
            _quotationContext.Include(quotation);

            var quotationDto = _mapper.Map<QuotationDto>(quotation);

            // 手动映射套餐数据
            if (quotation.Packages != null)
            {
                var packages = await quotation.Packages.GetListAsync();
                quotationDto.Packages = new List<ExaminationPackageDto>();

                foreach (var package in packages)
                {
                    _quotationContext.Include(package);
                    var packageDto = _mapper.Map<ExaminationPackageDto>(package);

                    // 手动映射组合项数据
                    if (package.CombinedItems != null)
                    {
                        var combinedItems = await package.CombinedItems.GetListAsync();
                        packageDto.CombinedItems = _mapper.Map<List<ExaminationCombinedItemDto>>(combinedItems);
                    }

                    quotationDto.Packages.Add(packageDto);
                }
            }

            return Ok(quotationDto);
        //}
        //catch (Exception)
        //{
        //    return Problem404Result("报价单未找到");
        //}
    }

    /// <summary>
    /// 分页获取报价单列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <returns>分页结果</returns>
    [HttpGet]
    public async Task<ActionResult<PageResultDto<QuotationDto>>> GetPage([FromQuery] QuotationQueryDto query)
    {
        if (query.PageIndex < 1 || query.PageSize < 1)
            return Problem400Result("分页参数错误");

        try
        {
            var (list, totalCount) = await _quotationRepository.GetPageAsync(
                query.PageIndex,
                query.PageSize,
                query.Keyword,
                query.Status,
                query.CompanyId,
                query.CreatorId,
                query.CreateTimeStart,
                query.CreateTimeEnd
            );

            var quotationDtos = _mapper.Map<List<QuotationDto>>(list);
            return Ok(new PageResultDto<QuotationDto>(query.PageIndex, query.PageSize, totalCount, quotationDtos));
        }
        catch (Exception ex)
        {
            return Problem400Result($"查询失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建报价单
    /// </summary>
    /// <param name="dto">创建报价单DTO</param>
    /// <param name="salesmanId">业务员ID</param>
    /// <returns>创建的报价单</returns>
    [HttpPost]
    //[TypeFilter(typeof(ValidateDtoAsyncActionFilter<CreateQuotationDto>))]
    [Validation(typeof(CreateQuotationDto), typeof(CreateQuotationDtoValidator))]
    [UnitOfWork]

    public async Task<ActionResult<QuotationDto>> Create([FromBody] CreateQuotationDto dto, [FromQuery] long salesmanId)
    {
        // 获取业务员
        var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == salesmanId);
        if (doctor == null)
        {
            return Problem404Result($"ID为{salesmanId}的业务员未找到");
        }

        var salesman = _quotationContext.AsSalesman(doctor);
        var quotation = _mapper.Map<Quotation>(dto);

        // 创建报价单
        await salesman.CreateQuotationAsync(quotation);

        // 添加套餐数据
        if (dto.Packages != null && dto.Packages.Count > 0)
        {
            var packages = _mapper.Map<List<ExaminationPackage>>(dto.Packages);
            await quotation.Packages.AddRangeAsync(packages);

            // 添加组合项数据
            for (int i = 0; i < packages.Count; i++)
            {
                if (dto.Packages[i].CombinedItems != null && dto.Packages[i].CombinedItems!.Count > 0)
                {
                    var combinedItems = _mapper.Map<List<ExaminationCombinedItem>>(dto.Packages[i].CombinedItems!);
                    await packages[i].CombinedItems.AddRangeAsync(combinedItems);
                }
            }
        }


        var result = _mapper.Map<QuotationDto>(quotation);
        return Ok(result);
    }

    /// <summary>
    /// 更新报价单
    /// </summary>
    /// <param name="dto">更新报价单DTO</param>
    /// <returns>操作结果</returns>
    [HttpPut]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<UpdateQuotationDto>))]
    public async Task<ActionResult> Update([FromBody] UpdateQuotationDto dto)
    {
        try
        {
            var quotation = await _quotationRepository.GetSingleAsync(q => q.Id == dto.Id);
            if (quotation.Status != QuotationStatus.Draft)
            {
                return Problem400Result("只能修改草稿状态的报价单");
            }

            // 更新基本信息
            quotation.CompanyId = dto.CompanyId;
            quotation.Remark = dto.Remark;

            await _quotationRepository.UpdateAsync(quotation);

            // 更新套餐数据（简化处理：先删除再添加）
            if (dto.Packages != null)
            {
                // 这里需要实现删除现有套餐和组合项的逻辑
                // 然后添加新的套餐和组合项
                // 为了简化，这里只做基本更新
            }

            return Ok();
        }
        catch (Exception ex)
        {
            return Problem400Result($"更新报价单失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除报价单
    /// </summary>
    /// <param name="id">报价单ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(long id)
    {
        try
        {
            var quotation = await _quotationRepository.GetSingleAsync(q => q.Id == id);
            if (quotation.Status != QuotationStatus.Draft)
            {
                return Problem400Result("只能删除草稿状态的报价单");
            }

            await _quotationRepository.DeleteAsync(quotation);
            return Ok();
        }
        catch (Exception)
        {
            return Problem404Result("报价单未找到");
        }
    }

    /// <summary>
    /// 提交报价单审核
    /// </summary>
    /// <param name="id">报价单ID</param>
    /// <param name="salesmanId">业务员ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/submit")]
    public async Task<ActionResult> SubmitForApproval(long id, [FromQuery] long salesmanId)
    {
        try
        {
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == salesmanId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{salesmanId}的业务员未找到");
            }

            var salesman = _quotationContext.AsSalesman(doctor);
            await salesman.SubmitForApprovalAsync(id);

            return Ok();
        }
        catch (Exception ex)
        {
            return Problem400Result($"提交审核失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 审核通过报价单
    /// </summary>
    /// <param name="id">报价单ID</param>
    /// <param name="approverId">审核员ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/approve")]
    public async Task<ActionResult> Approve(long id, [FromQuery] long approverId)
    {
        try
        {
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == approverId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{approverId}的审核员未找到");
            }

            var approver = _quotationContext.AsApprover(doctor);
            await approver.ApproveQuotationAsync(id);

            return Ok();
        }
        catch (Exception ex)
        {
            return Problem400Result($"审核失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 拒绝报价单
    /// </summary>
    /// <param name="id">报价单ID</param>
    /// <param name="approverId">审核员ID</param>
    /// <param name="reason">拒绝原因</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/reject")]
    public async Task<ActionResult> Reject(long id, [FromQuery] long approverId, [FromBody] string reason)
    {
        try
        {
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == approverId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{approverId}的审核员未找到");
            }

            var approver = _quotationContext.AsApprover(doctor);
            await approver.RejectQuotationAsync(id, reason);

            return Ok();
        }
        catch (Exception ex)
        {
            return Problem400Result($"拒绝失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取业务员的报价单列表
    /// </summary>
    /// <param name="salesmanId">业务员ID</param>
    /// <returns>报价单列表</returns>
    [HttpGet("salesman/{salesmanId}")]
    public async Task<ActionResult<List<QuotationDto>>> GetSalesmanQuotations(long salesmanId)
    {
        try
        {
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == salesmanId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{salesmanId}的业务员未找到");
            }

            var salesman = _quotationContext.AsSalesman(doctor);
            var quotations = await salesman.GetMyQuotationsAsync();
            var result = _mapper.Map<List<QuotationDto>>(quotations);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return Problem400Result($"获取报价单列表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取待审核的报价单列表
    /// </summary>
    /// <param name="approverId">审核员ID</param>
    /// <returns>待审核报价单列表</returns>
    [HttpGet("pending/{approverId}")]
    public async Task<ActionResult<List<QuotationDto>>> GetPendingQuotations(long approverId)
    {
        try
        {
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == approverId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{approverId}的审核员未找到");
            }

            var approver = _quotationContext.AsApprover(doctor);
            var quotations = await approver.GetPendingQuotationsAsync();
            var result = _mapper.Map<List<QuotationDto>>(quotations);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return Problem400Result($"获取待审核报价单列表失败: {ex.Message}");
        }
    }
}
