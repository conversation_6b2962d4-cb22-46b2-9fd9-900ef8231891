﻿using PE.Domain.ManagementRecordMng.Entities;

namespace PE.WebApi.Dtos.ManagementRecordMng;

public class CreateManagementRecordDto
{
    public long Id { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 健康管理助理ID
    /// </summary>
    public long? AssistantId { get; set; }

    /// <summary>
    /// 健康管理助理姓名
    /// </summary>
    public string? AssistantName { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    public long? ExpertId { get; set; }

    /// <summary>
    /// 专家姓名
    /// </summary>
    public string? ExpertName { get; set; }

    ///// <summary>
    ///// 状态（0：方案制定中；1：治疗中；2：完成）
    ///// </summary>
    //public ManagementRecordStatus Status { get; set; }
}
