﻿using FluentValidation;

namespace PE.WebApi.Dtos.DoctorMng
{
    public class DoctorDtoValidator : AbstractValidator<DoctorDto>
    {
        public DoctorDtoValidator()
        {
            RuleFor(x => x.Id).GreaterThan(0).WithMessage("Id无效");
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name不能为空");
            RuleFor(x => x.UserName).MinimumLength(5).WithMessage("UserName至少包含5个字符");
        }
    }


    public class CreateDoctorRequestValidator : AbstractValidator<CreateDoctorRequest>
    {
        public CreateDoctorRequestValidator()
        {
            RuleFor(x => x.UserName).MinimumLength(5).WithMessage("UserName至少包含5个字符");
            RuleFor(x => x.Password).MinimumLength(8).WithMessage("Password至少包含为8个字符");
            RuleFor(x => x.Name).NotEmpty().WithMessage("Name不能为空");
        }
    }
}
