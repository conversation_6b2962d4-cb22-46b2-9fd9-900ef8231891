﻿using StackExchange.Profiling;

namespace PE.WebApi.Middlewares
{
    public class MiniProfilerMiddleware
    {
        private readonly RequestDelegate _next;

        public MiniProfilerMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var profiler = MiniProfiler.Current;
            await _next(context);
            profiler?.Stop();
        }
    }
}
