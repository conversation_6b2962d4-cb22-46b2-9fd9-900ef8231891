﻿using PE.Domain.FollowUpMng.Entities;
using PE.Domain.FollowUpMng.Relations;

namespace PE.Infrastructure.FollowUpMng.Relations
{
    public class RecordOfFollowUpPlanDB : IRecordOfFollowUpPlan
    {
        HealthDbContext DbContext { get; set; }

        FollowUpPlan Plan { get; set; }

        public RecordOfFollowUpPlanDB(HealthDbContext dbContext, FollowUpPlan plan)
        {
            DbContext = dbContext;
            Plan = plan;
        }

        public Task<FollowUpRecord> GetPageAsync()
        {
            throw new NotImplementedException();
        }
    }
}
