﻿using System.Security.Claims;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.SystemMng.Entities;
using PE.WebApi.Dtos.DoctorMng;
using PE.WebApi.Dtos.SystemMng;
using PE.Common.Framework.ApiContext;
using PE.Common.Framework.Authentication;
using PE.Common.Framework.Encryption;

namespace PE.WebApi.Controllers.SystemMng;

public class UserController(IJwtProvider jwtProvider, IMapper mapper, IDoctorRepository doctorRepository)
    : BaseController
{
    private readonly IJwtProvider _jwtProvider = jwtProvider;
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;


    /// <summary>
    /// 医生登录
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [HttpPost("doctor/login")]
    public async Task<ActionResult<LoginResult<DoctorDto>>> Login4Doctor([FromBody] LoginRequest req)
    {
        req.Password = Md5Encryptor.GetMD5Hash(req.Password!, Md5Encryptor.DefaultSalt);
        Doctor? user = await _doctorRepository.GetSingleAsync(x =>
            x.UserName == req.UserName && x.Password == req.Password
        );
        if (user is null)
        {
            return Problem404Result("用户不存在或密码错误");
        }

        _doctorRepository.InUserContext().Include(user);
        Role? role = await user.Role!.GetSingleAsync();
        int[] memuIds = role?.MenuIds ?? [];

        List<Claim> claims = GenerateClaims(user);
        string token = _jwtProvider.GenerateToken(claims);

        return Ok(new LoginResult<DoctorDto>(_mapper.Map<DoctorDto>(user), token,
            _jwtProvider.Options.Expired, memuIds));
    }


    private static List<Claim> GenerateClaims(Doctor user)
    {
        return [
            new Claim(HealthClaimTypes.Id, user.Id.ToString()),
            //new Claim(HealthClaimTypes.Name, user.Name),
            new Claim(HealthClaimTypes.Role, AuthorizePolicies.Doctor)
        ];
    }
}
