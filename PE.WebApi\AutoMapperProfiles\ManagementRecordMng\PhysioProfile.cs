using AutoMapper;
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Dtos.ManagementRecordMng;

namespace PE.WebApi.AutoMapperProfiles.ManagementRecordMng
{
    public class PhysioProfile : Profile
    {
        public PhysioProfile()
        {
            // 开始理疗项目DTO到实体的映射
            CreateMap<StartDetailDto, ScheduleDetail>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.DetailId)) // 将DetailId映射到Id
                .ForMember(dest => dest.PhysioId, opt => opt.Ignore()) // 由Physio.StartDetail方法设置
                .ForMember(dest => dest.PhysioName, opt => opt.Ignore()) // 由Physio.StartDetail方法设置
                .ForMember(dest => dest.BegingTime, opt => opt.Ignore()) // 由Physio.StartDetail方法设置
                .ForMember(dest => dest.EndTime, opt => opt.Ignore())
                .ForMember(dest => dest.IsComplete, opt => opt.Ignore())
                .ForMember(dest => dest.ItemId, opt => opt.Ignore())
                .ForMember(dest => dest.ItemName, opt => opt.Ignore())
                .ForMember(dest => dest.ItemRemark, opt => opt.Ignore());

            // 完成理疗项目DTO到实体的映射
            CreateMap<CompleteDetailDto, ScheduleDetail>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.DetailId)) // 将DetailId映射到Id
                .ForMember(dest => dest.PhysioId, opt => opt.Ignore())
                .ForMember(dest => dest.PhysioName, opt => opt.Ignore())
                .ForMember(dest => dest.BegingTime, opt => opt.Ignore())
                .ForMember(dest => dest.EndTime, opt => opt.Ignore()) // 由Physio.CompleteDetail方法设置
                .ForMember(dest => dest.IsComplete, opt => opt.Ignore()) // 由Physio.CompleteDetail方法设置
                .ForMember(dest => dest.ItemId, opt => opt.Ignore())
                .ForMember(dest => dest.ItemName, opt => opt.Ignore())
                .ForMember(dest => dest.ItemRemark, opt => opt.Ignore());
        }
    }
}
