﻿using FluentValidation;
using PE.Domain.ManagementRecordMng.Entities;

namespace PE.WebApi.Dtos.ManagementRecordMng
{
    /// <summary>
    /// 预约分页查询入参
    /// </summary>
    public class Page4AppointmentDto
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 助理ID
        /// </summary>
        public long? AssistantId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public long? CustomerId { get; set; }

        /// <summary>
        /// 预约日期开始
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 预约日期结束
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public AppointmentStatus? Status { get; set; }
    }

    public class Page4AppointmentDtoValidator : AbstractValidator<Page4AppointmentDto>
    {
        public Page4AppointmentDtoValidator()
        {
            RuleFor(x => x.PageIndex).GreaterThan(0).WithMessage("页码必须大于0");
            RuleFor(x => x.PageSize).GreaterThan(0).WithMessage("页大小必须大于0");

            When(x => x.StartDate.HasValue && x.EndDate.HasValue, () => {
                RuleFor(x => x.EndDate)
                    .GreaterThanOrEqualTo(x => x.StartDate)
                    .WithMessage("结束日期必须大于或等于开始日期");
            });
        }
    }
}
