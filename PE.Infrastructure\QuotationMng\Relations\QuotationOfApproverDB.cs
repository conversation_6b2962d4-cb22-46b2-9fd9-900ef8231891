using System.Linq.Expressions;
using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.Domain.QuotationMng.Relations;

namespace PE.Infrastructure.QuotationMng.Relations;

public class QuotationOfApproverDB(HealthDbContext db<PERSON><PERSON><PERSON><PERSON>, Doctor master)
    : BaseRelation<Doctor, Quotation>(db<PERSON><PERSON><PERSON><PERSON>, master), IQuotationOfApprover
{
    public async Task<List<Quotation>> GetPendingQuotationsAsync()
    {
        return await DbContext.Db.Queryable<Quotation>()
            .Where(q => q.Status == QuotationStatus.PendingApproval)
            .OrderBy(q => q.CreateTime)
            .ToListAsync();
    }

    public async Task<List<Quotation>> GetApprovedQuotationsAsync()
    {
        return await DbContext.Db.Queryable<Quotation>()
            .Where(q => q.AuditorId == Master.Id && q.Status == QuotationStatus.Approved)
            .OrderByDescending(q => q.AuditTime)
            .ToListAsync();
    }

    public async Task ApproveQuotationAsync(long quotationId)
    {
        var quotation = await DbContext.Db.Queryable<Quotation>()
            .Where(q => q.Id == quotationId)
            .FirstAsync();

        if (quotation != null)
        {
            quotation.Status = QuotationStatus.Approved;
            quotation.AuditorId = (int)Master.Id;
            quotation.AuditorName = Master.Name!;
            quotation.AuditTime = DateTime.Now;

            await DbContext.Db.Updateable(quotation).ExecuteCommandAsync();
        }
    }
}
