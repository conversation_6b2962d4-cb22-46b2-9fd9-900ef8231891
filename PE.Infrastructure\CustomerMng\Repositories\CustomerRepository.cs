﻿using PE.Common.Utils;
using PE.Domain.CustomerMng.Entities;
using PE.Domain.CustomerMng.Repositories;

namespace PE.Infrastructure.CustomerMng.Repositories
{
    public class CustomerRepository(HealthDbContext dbContext)
        : BaseRepository<Customer>(dbContext), ICustomerRepository
    {
        //public override Task IncludeAsync(Customer entity)
        //{
        //    throw new NotImplementedException();
        //}

        public override async Task AddAsync(Customer entity)
        {
            entity.Id = await DbContext.Customers.InsertReturnBigIdentityAsync(entity);
        }

        public async Task<(List<Customer>, int)> GetPageAsync(int pageIndex, int pageSize, string? keyword)
        {
            var totalCount = new RefAsync<int>();

            var list = await DbContext.Customers.AsQueryable()
                .WhereIF(keyword.IsNotNullOrEmpty(), c => c.Name.Contains(keyword!) || c.PhoneNo.Contains(keyword!))
                .ToPageListAsync(pageIndex, pageSize, totalCount);

            return (list, totalCount);
        }
    }
}
