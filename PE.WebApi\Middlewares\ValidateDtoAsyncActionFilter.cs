﻿using System.Text;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace PE.WebApi.Middlewares;

public class ValidateDtoAsyncActionFilter<T>(IValidator<T> validator) : IAsyncActionFilter
    where T : class
{
    private readonly IValidator<T> _validator = validator;


    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var parameter = context.ActionArguments.FirstOrDefault(p => p.Value is T);
        if (parameter.Value != null)
        {
            var value = parameter.Value as T;
            var validationContext = new ValidationContext<T>(value!);
            var result = await _validator.ValidateAsync(validationContext);

            if (!result.IsValid)
            {
                foreach (var failure in result.Errors)
                {
                    context.ModelState.AddModelError(failure.PropertyName, failure.ErrorMessage);
                }

                var problem = new ProblemDetails
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = "Invalid arguments",
                    Detail = ParseToString(context.ModelState)
                };

                context.Result = new BadRequestObjectResult(problem);
                return;
            }
        }

        await next();
    }


    static string ParseToString(ModelStateDictionary dict)
    {
        StringBuilder sb = new();
        foreach (var key in dict.Keys)
        {
            var errors = dict[key]!.Errors;
            foreach (var error in errors)
            {
                sb.Append($"{error.ErrorMessage}；");
            }
        }
        return sb.ToString().TrimEnd('；');
    }
}


[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class ValidationAttribute : ActionFilterAttribute
{
    private readonly Type _dtoType;
    private readonly Type _validatorType;

    public ValidationAttribute(Type dtoType, Type validatorType)
    {
        if (!typeof(IValidator).IsAssignableFrom(validatorType))
        {
            throw new ArgumentException("Validator type must implement IValidator", nameof(validatorType));
        }

        _dtoType = dtoType;
        _validatorType = validatorType;
    }

    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // 从依赖注入容器获取验证器
        var serviceProvider = context.HttpContext.RequestServices;
        var validator = serviceProvider.GetRequiredService(_validatorType) as IValidator;

        if (validator == null)
        {
            throw new InvalidOperationException($"Validator of type {_validatorType.Name} not found.");
        }

        // 查找匹配类型的参数
        var parameter = context.ActionArguments
            .FirstOrDefault(p => p.Value != null && _dtoType.IsInstanceOfType(p.Value));

        if (parameter.Value != null)
        {
            // 创建验证上下文
            var validationContextType = typeof(ValidationContext<>).MakeGenericType(_dtoType);
            var validationContext = Activator.CreateInstance(
                validationContextType,
                parameter.Value
            );

            // 获取并调用验证方法
            var validateMethod = validator.GetType()
                .GetMethod("ValidateAsync", new[] { validationContextType, typeof(CancellationToken) });

            if (validateMethod == null)
            {
                throw new InvalidOperationException("ValidateAsync method not found on validator.");
            }

            // 执行异步验证
            var validationTask = validateMethod.Invoke(
                validator,
                new[] { validationContext, CancellationToken.None }
            ) as Task<ValidationResult>;

            if (validationTask == null)
            {
                throw new InvalidOperationException("Failed to execute validation task.");
            }

            var validationResult = await validationTask;

            if (!validationResult.IsValid)
            {
                // 添加验证错误到 ModelState
                foreach (var error in validationResult.Errors)
                {
                    context.ModelState.AddModelError(error.PropertyName, error.ErrorMessage);
                }

                // 返回 400 BadRequest
                var problemDetails = new ProblemDetails
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = "Validation Error",
                    Detail = FormatErrorMessages(context.ModelState)
                };

                context.Result = new BadRequestObjectResult(problemDetails);
                return;
            }
        }

        await next();
    }

    private static string FormatErrorMessages(ModelStateDictionary modelState)
    {
        return string.Join("；", modelState.Values
            .SelectMany(v => v.Errors)
            .Select(e => e.ErrorMessage));
    }
}