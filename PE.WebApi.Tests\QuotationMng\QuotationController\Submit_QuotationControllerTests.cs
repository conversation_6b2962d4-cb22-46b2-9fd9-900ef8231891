using PE.Domain.DoctorMng.Entities;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Tests.Fakers.QuotationMng;
using Humanizer;

namespace PE.WebApi.Tests.QuotationMng.QuotationController;

[Collection(nameof(DatabaseCollection))]
public class Submit_QuotationControllerTests(CustomWebApplicationFactory factory) : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly CreateQuotationDtoFaker _faker = new();
    private Doctor? _stubDoctor;
    private QuotationDto? _stubQuotation;

    [Fact]
    public async Task Submit_返回成功_当报价单为草稿状态时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var url = Urls.Quotation.Submit.FormatWith(_stubQuotation!.Id) + $"?salesmanId={_stubDoctor!.Id}";

        // Act
        var response = await client.PostAsync(url, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        // 验证状态已更改
        var getUrl = Urls.Quotation.Get.FormatWith(_stubQuotation.Id);
        var getResponse = await client.GetAsync(getUrl);
        getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        var updatedQuotation = await getResponse.Content.ReadFromJsonAsync<QuotationDto>();
        updatedQuotation!.Status.Should().Be(Domain.QuotationMng.Entities.QuotationStatus.PendingApproval);
    }

    [Fact]
    public async Task Submit_返回404_当业务员不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var nonExistentSalesmanId = 99999;
        var url = Urls.Quotation.Submit.FormatWith(_stubQuotation!.Id) + $"?salesmanId={nonExistentSalesmanId}";

        // Act
        var response = await client.PostAsync(url, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task Submit_返回404_当报价单不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var nonExistentQuotationId = 99999;
        var url = Urls.Quotation.Submit.FormatWith(nonExistentQuotationId) + $"?salesmanId={_stubDoctor!.Id}";

        // Act
        var response = await client.PostAsync(url, null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    public async Task InitializeAsync()
    {
        var client = _factory.CreateClient();

        // 创建测试用的医生数据
        var doctorDto = new CreateDoctorRequestFaker().Generate();
        var doctorResponse = await client.PostAsJsonAsync(Urls.Doctor.Create, doctorDto);
        doctorResponse.EnsureSuccessStatusCode();
        _stubDoctor = await doctorResponse.Content.ReadFromJsonAsync<Doctor>();

        // 创建测试用的报价单数据
        var quotationDto = _faker.Generate();
        var quotationUrl = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";
        var quotationResponse = await client.PostAsJsonAsync(quotationUrl, quotationDto);
        quotationResponse.EnsureSuccessStatusCode();
        _stubQuotation = await quotationResponse.Content.ReadFromJsonAsync<QuotationDto>();
    }

    public Task DisposeAsync()
    {
        return Task.CompletedTask;
    }
}
