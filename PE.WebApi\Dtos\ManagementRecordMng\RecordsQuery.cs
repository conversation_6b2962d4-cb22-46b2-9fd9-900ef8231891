﻿using PE.Domain.ManagementRecordMng.Entities;

namespace PE.WebApi.Dtos.ManagementRecordMng;

public class RecordsQuery
{
    /// <summary>
    /// 客户Id
    /// </summary>
    public long? CustomerId { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    public long? ExpertId { get; set; }

    /// <summary>
    /// 助理Id
    /// </summary>
    public long? AssistantId { get; set; }

    /// <summary>
    /// 状态（不传：全部；1：新建；2：治疗中；3：完成）
    /// </summary>
    public ManagementRecordStatus Status { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 分页大小
    /// </summary>
    public int PageSize { get; set; }
}
