﻿using System.Linq.Expressions;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Relations;
using PE.Common.Exceptions;

namespace PE.Infrastructure.ManagementRecordMng.Relations;

/// <summary>
/// 疗程的日程
/// </summary>
/// <param name="dbContext"></param>
/// <param name="master"></param>
public class ScheduleOfCourseDB(HealthDbContext dbContext, Course master)
    : BaseRelation<Course, CourseSchedule>(dbContext, master), IScheduleOfCourse
{
    public async Task<List<CourseSchedule>> GetListAsync()
    {
        var list = await base.GetListAsync();
        return list.OrderBy(x => x.SerialNo).ToList();
    }

    public override async Task<List<CourseSchedule>> GetListAsync(Expression<Func<CourseSchedule, bool>>? expression = null)
    {
        //return DbContext.CourseSchedules.AsQueryable()
        //    .Where(expression)
        //    .OrderBy(x => x.SerialNo)
        //    .ToListAsync();

        var list = await DbContext.CourseSchedules.AsQueryable()
            .Where(expression)
            .OrderBy(x => x.SerialNo)
            .ToListAsync();

        foreach (var schedule in list)
        {
            schedule.Details = new DetailOfSchedule(DbContext, schedule);
        }

        return list;
    }

    public override async Task<CourseSchedule> GetSingleAsync(Expression<Func<CourseSchedule, bool>>? expression = null)
    {
        var schedule = await base.GetSingleAsync(expression) ?? throw new NotFoundException("日程信息未找到");
        schedule.Details = new DetailOfSchedule(DbContext, schedule);

        return schedule;
    }


    public async Task DesignScheduleAsync(CourseSchedule schedule)
    {
        var oldSchedule = await DbContext.CourseSchedules.GetSingleAsync(x => x.Id == schedule.Id);
    }

    public async Task ClearDetailsAsync()
    {
        await DbContext.ScheduleDetails.DeleteAsync(d => d.CourseId == Master.Id);
    }

    public async Task AddDetailsAsync(List<ScheduleDetail> allDetails)
    {
        await DbContext.ScheduleDetails.InsertRangeAsync(allDetails);
    }

    public async Task<List<ScheduleDetail>> GetDetailsAsync()
    {
        return await DbContext.ScheduleDetails.AsQueryable()
            .Where(d => d.CourseId == Master.Id)
            .ToListAsync();
    }
}
