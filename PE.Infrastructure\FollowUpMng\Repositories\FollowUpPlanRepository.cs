﻿using PE.Domain.FollowUpMng.Entities;
using PE.Domain.FollowUpMng.Repositories;
using PE.Infrastructure.FollowUpMng.Relations;

namespace PE.Infrastructure.FollowUpMng.Repositories
{
    public class FollowUpPlanRepository : BaseRepository<FollowUpPlan>, IFollowUpPlanRepository
    {
        public FollowUpPlanRepository(HealthDbContext dbContext) : base(dbContext)
        {

        }

        //public override Task IncludeAsync(FollowUpPlan entity)
        //{
        //    entity.SetRelations(new RecordOfFollowUpPlanDB(DbContext, entity));

        //    return Task.CompletedTask;
        //}
    }
}
