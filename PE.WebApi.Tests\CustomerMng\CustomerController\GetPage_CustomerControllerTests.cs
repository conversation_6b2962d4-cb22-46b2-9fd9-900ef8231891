﻿using PE.Domain.CustomerMng.Entities;
using PE.WebApi.Dtos.CutomerMng;

namespace PE.WebApi.Tests.CustomerMng.CustomerController
{
    [Collection(nameof(DatabaseCollection))]
    public class GetPage_CustomerControllerTests(CustomWebApplicationFactory factory)
        : IAsyncLifetime
    {
        private readonly CustomWebApplicationFactory _factory = factory;
        private readonly CustomerDtoFaker _faker = new();


        [Fact]
        public async Task GetPage_返回分页列表_当数据存在时()
        {
            var client = _factory.CreateClient();

            var response = await client.GetAsync($"{Urls.Customer.GetPage}/?pageIndex=2&pageSize=3");

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await response.Content.ReadFromJsonAsync<PageResultDto<CustomerDto>>();
            result!.PageIndex.Should().Be(2);
            result!.PageSize.Should().Be(3);
            result!.TotalCount.Should().Be(5);
            result!.Items.Count.Should().Be(2);
        }

        [Fact]
        public async Task GetPage_返回400_当分页参数错误时()
        {
            var client = _factory.CreateClient();

            var response = await client.GetAsync($"{Urls.Customer.GetPage}/?pageIndex=0&pageSize=0");

            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetPage_返回空列表_当页码超出最大页码时()
        {
            var client = _factory.CreateClient();

            var response = await client.GetAsync($"{Urls.Customer.GetPage}/?pageIndex=3&pageSize=3");

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var result = await response.Content.ReadFromJsonAsync<PageResultDto<CustomerDto>>();
            result!.PageIndex.Should().Be(3);
            result!.PageSize.Should().Be(3);
            result!.TotalCount.Should().Be(5);
            result!.Items.Count.Should().Be(0);
        }


        public async Task InitializeAsync()
        {
            var client = _factory.CreateClient();
            for (int i = 0; i < 5; i++)
            {
                var c = _faker.Generate();
                var rsp = await client.PostAsJsonAsync(Urls.Customer.Create, c);
                rsp.StatusCode.Should().Be(HttpStatusCode.OK);
            }
        }

        public async Task DisposeAsync()
        {
            await TestDataCleaner.CleanAsync<Customer>();
        }
    }
}
