﻿using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 健康管理主记录
/// </summary>
[SugarTable("core_management_record", TableDescription = "健康管理主记录")]
[SugarIndex("index_{table}_customer_id", nameof(CustomerId), OrderByType.Desc)] // 客户Id索引
[SugarIndex("index_{table}_assistant_id", nameof(AssistantId), OrderByType.Desc)] // 助理Id索引
[SugarIndex("index_{table}_expert_id", nameof(ExpertId), OrderByType.Desc)] // 专家Id索引
public class ManagementRecord : Entity, IAggregateRoot
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    [SugarColumn(ColumnName = "customer_id", ColumnDescription = "客户ID")]
    public long CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    [SugarColumn(ColumnName = "customer_name", ColumnDescription = "客户姓名")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 健康管理助理ID
    /// </summary>
    [SugarColumn(ColumnName = "assistant_id", ColumnDescription = "助理Id")]
    public long? AssistantId { get; set; }

    /// <summary>
    /// 健康管理助理姓名
    /// </summary>
    [SugarColumn(ColumnName = "assistant_name", ColumnDescription = "助理姓名")]
    public string? AssistantName { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    [SugarColumn(ColumnName = "expert_id", ColumnDescription = "专家Id")]
    public long? ExpertId { get; set; }

    /// <summary>
    /// 专家姓名
    /// </summary>
    [SugarColumn(ColumnName = "expert_name", ColumnDescription = "专家姓名")]
    public string? ExpertName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime? CreationTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [SugarColumn(ColumnName = "completion_time", ColumnDescription = "完成时间")]
    public DateTime? CompletionTime { get; set; }

    /// <summary>
    /// 状态（1：新建；2：治疗中；3：完成）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public ManagementRecordStatus Status { get; set; } = ManagementRecordStatus.New;

    /// <summary>
    /// 疗程
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public ICourseOfManagementRecord? Courses { get; set; }

    /// <summary>
    /// 个人信息问卷
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IQuestionnaireOfManagementRecord Questionnaire { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
