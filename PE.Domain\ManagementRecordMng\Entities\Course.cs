﻿using PE.Domain.ManagementRecordMng.Relations;

namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 疗程
/// </summary>
[SugarTable("physio_course", TableDescription = "理疗疗程")]
[SugarIndex("index_{table}_management_record_id", nameof(ManagementRecordId), OrderByType.Desc)]
public class Course : Entity
{
    /// <summary>
    /// Id
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 管理记录Id
    /// </summary>
    [SugarColumn(ColumnName = "management_record_id", ColumnDescription = "管理记录Id")]
    public long ManagementRecordId { get; set; }

    /// <summary>
    /// 疗程序号
    /// </summary>
    [SugarColumn(ColumnName = "serial_no", ColumnDescription = "疗程序号")]
    public int SerialNo { get; set; }

    /// <summary>
    /// 健康管理助理ID
    /// </summary>
    [SugarColumn(ColumnName = "assistant_id", ColumnDescription = "助理Id")]
    public long? AssistantId { get; set; }

    /// <summary>
    /// 健康管理助理姓名
    /// </summary>
    [SugarColumn(ColumnName = "assistant_name", ColumnDescription = "助理姓名")]
    public string? AssistantName { get; set; }

    /// <summary>
    /// 专家Id
    /// </summary>
    [SugarColumn(ColumnName = "expert_id", ColumnDescription = "专家Id")]
    public long? ExpertId { get; set; }

    /// <summary>
    /// 专家姓名
    /// </summary>
    [SugarColumn(ColumnName = "expert_name", ColumnDescription = "专家姓名")]
    public string? ExpertName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime? CreationTime { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    [SugarColumn(ColumnName = "completion_time", ColumnDescription = "完成时间")]
    public DateTime? CompletionTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public CourseStatus Status { get; set; } = CourseStatus.New;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注")]
    public string? Remark { get; set; }

    /// <summary>
    /// 客户评价
    /// </summary>
    [SugarColumn(ColumnName = "reviews", ColumnDescription = "客户评价")]
    public string? Reviews { get; set; }

    /// <summary>
    /// 周期
    /// </summary>
    [SugarColumn(ColumnName = "cycle", ColumnDescription = "周期")]
    public int? Cycle { get; set; }

    /// <summary>
    /// 日程
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public IScheduleOfCourse? Schedules { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}
