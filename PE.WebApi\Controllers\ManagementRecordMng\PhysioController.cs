using AutoMapper;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng.Entities;
using PE.WebApi.Dtos.ManagementRecordMng;
using PE.WebApi.Middlewares;
using PE.Common.Exceptions;
using Microsoft.AspNetCore.Mvc;

namespace PE.WebApi.Controllers.ManagementRecordMng;

/// <summary>
/// 理疗师控制器
/// </summary>
public class PhysioController(IMapper mapper, IDoctorRepository doctorRepository) : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;

    /// <summary>
    /// 开始理疗项目
    /// </summary>
    /// <param name="dto">理疗项目信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("startDetail")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<StartDetailDto>))]
    public async Task<ActionResult> StartDetail([FromBody] StartDetailDto dto)
    {
        try
        {
            // 获取理疗师
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == dto.PhysioId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{dto.PhysioId}的理疗师未找到");
            }

            // 创建理疗项目明细
            var detail = _mapper.Map<ScheduleDetail>(dto);
            var physio = _doctorRepository.InManagementContext().AsPhysio(doctor);

            // 开始理疗项目，同时更新预约状态为已报到
            await physio.StartDetail(dto.RecordId, dto.CourseId, detail);

            return Ok();
        }
        catch (DomainException ex)
        {
            return Problem400Result(ex.Message);
        }
        catch (Exception ex)
        {
            return Problem400Result(ex.Message);
        }
    }

    /// <summary>
    /// 完成理疗项目
    /// </summary>
    /// <param name="dto">理疗项目信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("completeDetail")]
    [TypeFilter(typeof(ValidateDtoAsyncActionFilter<CompleteDetailDto>))]
    public async Task<ActionResult> CompleteDetail([FromBody] CompleteDetailDto dto)
    {
        try
        {
            // 获取理疗师
            var doctor = await _doctorRepository.GetSingleAsync(d => d.Id == dto.PhysioId);
            if (doctor == null)
            {
                return Problem404Result($"ID为{dto.PhysioId}的理疗师未找到");
            }

            // 创建理疗项目明细
            var detail = _mapper.Map<ScheduleDetail>(dto);
            var physio = _doctorRepository.InManagementContext().AsPhysio(doctor);

            // 完成理疗项目
            await physio.CompleteDetail(dto.RecordId, dto.CourseId, detail);

            return Ok();
        }
        catch (DomainException ex)
        {
            return Problem400Result(ex.Message);
        }
    }
}
