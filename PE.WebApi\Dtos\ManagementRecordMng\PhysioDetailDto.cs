using FluentValidation;

namespace PE.WebApi.Dtos.ManagementRecordMng
{
    /// <summary>
    /// 开始理疗项目请求
    /// </summary>
    public class StartDetailDto
    {
        /// <summary>
        /// 理疗师ID
        /// </summary>
        public long PhysioId { get; set; }

        /// <summary>
        /// 管理记录ID
        /// </summary>
        public long RecordId { get; set; }

        /// <summary>
        /// 疗程ID
        /// </summary>
        public long CourseId { get; set; }

        /// <summary>
        /// 日程明细ID
        /// </summary>
        public long DetailId { get; set; }

        /// <summary>
        /// 日程ID
        /// </summary>
        public long ScheduleId { get; set; }

        /// <summary>
        /// 执行备注
        /// </summary>
        public string? ExecRemark { get; set; }
    }

    /// <summary>
    /// 开始理疗项目请求验证器
    /// </summary>
    public class StartDetailDtoValidator : AbstractValidator<StartDetailDto>
    {
        public StartDetailDtoValidator()
        {
            RuleFor(x => x.PhysioId).GreaterThan(0).WithMessage("理疗师ID必须大于0");
            RuleFor(x => x.RecordId).GreaterThan(0).WithMessage("管理记录ID必须大于0");
            RuleFor(x => x.CourseId).GreaterThan(0).WithMessage("疗程ID必须大于0");
            RuleFor(x => x.DetailId).GreaterThan(0).WithMessage("日程明细ID必须大于0");
            RuleFor(x => x.ScheduleId).GreaterThan(0).WithMessage("日程ID必须大于0");
        }
    }

    /// <summary>
    /// 完成理疗项目请求
    /// </summary>
    public class CompleteDetailDto
    {
        /// <summary>
        /// 理疗师ID
        /// </summary>
        public long PhysioId { get; set; }

        /// <summary>
        /// 管理记录ID
        /// </summary>
        public long RecordId { get; set; }

        /// <summary>
        /// 疗程ID
        /// </summary>
        public long CourseId { get; set; }

        /// <summary>
        /// 日程明细ID
        /// </summary>
        public long DetailId { get; set; }

        /// <summary>
        /// 日程ID
        /// </summary>
        public long ScheduleId { get; set; }

        /// <summary>
        /// 执行备注
        /// </summary>
        public string? ExecRemark { get; set; }
    }

    /// <summary>
    /// 完成理疗项目请求验证器
    /// </summary>
    public class CompleteDetailDtoValidator : AbstractValidator<CompleteDetailDto>
    {
        public CompleteDetailDtoValidator()
        {
            RuleFor(x => x.PhysioId).GreaterThan(0).WithMessage("理疗师ID必须大于0");
            RuleFor(x => x.RecordId).GreaterThan(0).WithMessage("管理记录ID必须大于0");
            RuleFor(x => x.CourseId).GreaterThan(0).WithMessage("疗程ID必须大于0");
            RuleFor(x => x.DetailId).GreaterThan(0).WithMessage("日程明细ID必须大于0");
            RuleFor(x => x.ScheduleId).GreaterThan(0).WithMessage("日程ID必须大于0");
        }
    }
}
