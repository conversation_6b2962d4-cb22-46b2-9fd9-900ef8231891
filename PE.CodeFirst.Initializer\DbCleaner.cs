﻿using PE.Domain.Abstractions;
using PE.Domain.CustomerMng.Entities;
using SqlSugar;

namespace PE.CodeFirst.Initializer
{
    public class DbCleaner(string connecetionString, DbType dbType)
    {
        public string ConnecetionString { get; } = connecetionString;
        public DbType DbType { get; } = dbType;

        public void Clean()
        {
            SqlSugarClient db = DbClientFactory.GetClient(ConnecetionString, DbType);

            var types = typeof(Customer).Assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Entity)))
                .ToArray();

            db.DbMaintenance.DropTable(types);
        }
    }
}
