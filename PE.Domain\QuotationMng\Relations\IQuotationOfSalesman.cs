using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;

namespace PE.Domain.QuotationMng.Relations;

/// <summary>
/// 业务员的报价单关系
/// </summary>
public interface IQuotationOfSalesman : IBaseRelation<Doctor, Quotation>
{
    /// <summary>
    /// 获取我的报价单列表
    /// </summary>
    /// <returns>报价单列表</returns>
    Task<List<Quotation>> GetMyQuotationsAsync();

    /// <summary>
    /// 获取草稿状态的报价单列表
    /// </summary>
    /// <returns>草稿报价单列表</returns>
    Task<List<Quotation>> GetDraftQuotationsAsync();
}
