using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;

namespace PE.Domain.QuotationMng.Relations;

/// <summary>
/// 审核员的报价单关系
/// </summary>
public interface IQuotationOfApprover : IBaseRelation<Doctor, Quotation>
{
    /// <summary>
    /// 获取待审核的报价单列表
    /// </summary>
    /// <returns>待审核报价单列表</returns>
    Task<List<Quotation>> GetPendingQuotationsAsync();

    /// <summary>
    /// 获取已审核的报价单列表
    /// </summary>
    /// <returns>已审核报价单列表</returns>
    Task<List<Quotation>> GetApprovedQuotationsAsync();

    /// <summary>
    /// 审核报价单
    /// </summary>
    /// <param name="quotationId">报价单ID</param>
    /// <returns></returns>
    Task ApproveQuotationAsync(long quotationId);
}
