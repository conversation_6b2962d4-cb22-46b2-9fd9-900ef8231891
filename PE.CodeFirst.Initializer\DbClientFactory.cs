﻿// #define MsSql

using SqlSugar;

namespace PE.CodeFirst.Initializer
{
    internal static class DbClientFactory
    {
        public static SqlSugarClient GetClient(string connecetionString, DbType dbType)
        {
            ConnectionConfig cfg = new()
            {
                ConnectionString = connecetionString,
                DbType = dbType,
                IsAutoCloseConnection = true,
            };
            return new SqlSugarClient(cfg);
        }
    }

    internal class DbClientBuilder
    {
        private readonly ConnectionConfig _config;

        public DbClientBuilder(string connecetionString, DbType dbType)
        {
            _config = new()
            {
                ConnectionString = connecetionString,
                DbType = dbType,
                IsAutoCloseConnection = true,

#if MsSql
                MoreSettings = new ConnMoreSettings()
                {
                    SqlServerCodeFirstNvarchar = true
                }
#endif

            };
        }

        public DbClientBuilder SetConfigureExternalServices(ConfigureExternalServices ceServices)
        {
            _config.ConfigureExternalServices = ceServices;
            return this;
        }

        public SqlSugarClient Build()
        {
            return new SqlSugarClient(_config);
        }
    }
}
