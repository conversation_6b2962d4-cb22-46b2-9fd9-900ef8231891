﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <NoWarn>1701;1702</NoWarn>
    <WarningsNotAsErrors>8618</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <NoWarn>1701;1702</NoWarn>
    <WarningsNotAsErrors>8618</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\PE.Domain.Abstractions\PE.Domain.Abstractions.csproj" />
    <ProjectReference Include="..\PE.Common\PE.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CustomerMng\Relations\" />
    <Folder Include="DoctorMng\Relations\" />
    <Folder Include="QuestionnaireMng\Entities\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="2.2.0" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

</Project>
