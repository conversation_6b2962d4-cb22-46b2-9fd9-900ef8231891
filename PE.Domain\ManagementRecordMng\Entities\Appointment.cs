﻿using SqlSugar;

namespace PE.Domain.ManagementRecordMng.Entities;

/// <summary>
/// 预约
/// </summary>
[SugarTable("physio_appointment", TableDescription = "预约")]
[SugarIndex("index_{table}_schedule_id", nameof(ScheduleId), OrderByType.Desc)]
[SugarIndex("index_{table}_assistant_id", nameof(AssistantId), OrderByType.Desc)]
public class Appointment : Entity, IAggregateRoot
{
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 日程Id
    /// </summary>
    [SugarColumn(ColumnName = "schedule_id", ColumnDescription = "日程Id")]
    public long ScheduleId { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    [SugarColumn(ColumnName = "customer_id", ColumnDescription = "客户Id")]
    public long? CustomerId { get; set; }

    /// <summary>
    /// 客户姓名
    /// </summary>
    [SugarColumn(ColumnName = "customer_name", ColumnDescription = "客户姓名")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// 预约日期
    /// </summary>
    [SugarColumn(ColumnName = "appointment_date", ColumnDescription = "预约日期")]
    public DateTime AppointmentDate { get; set; }

    /// <summary>
    /// 上下午
    /// </summary>
    [SugarColumn(ColumnName = "meridiem_type", ColumnDescription = "上下午")]
    public int MeridiemType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public AppointmentStatus Status { get; set; } = AppointmentStatus.Booked;

    /// <summary>
    /// 助理ID
    /// </summary>
    [SugarColumn(ColumnName = "assistant_id", ColumnDescription = "助理Id")]
    public long? AssistantId { get; set; }

    /// <summary>
    /// 助理姓名
    /// </summary>
    [SugarColumn(ColumnName = "assistant_name", ColumnDescription = "助理姓名")]
    public string? AssistantName { get; set; }

    /// <summary>
    /// 预约记录创建人Id
    /// </summary>
    [SugarColumn(ColumnName = "creator_id", ColumnDescription = "创建人Id")]
    public long CreatorId { get; set; }

    /// <summary>
    /// 预约记录创建人姓名
    /// </summary>
    [SugarColumn(ColumnName = "creator_name", ColumnDescription = "创建人姓名")]
    public string? CreatorName { get; set; }

    /// <summary>
    /// 预约记录创建时间
    /// </summary>
    [SugarColumn(ColumnName = "creation_time", ColumnDescription = "创建时间")]
    public DateTime CreationTime { get; set; } = DateTime.Now;

    public override object GetIdentity()
    {
        return Id;
    }
}
