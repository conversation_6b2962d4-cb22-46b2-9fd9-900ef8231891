﻿using System.Security.Cryptography;
using System.Text;

namespace PE.Common.Framework.Encryption
{
    public static class Md5Encryptor
    {
        public const string DefaultSalt = "Health1234567890";


        public static string GetMD5Hash(string input, string? salt = "")
        {
            byte[] inputBytes = Encoding.ASCII.GetBytes(input + salt ?? "");
            byte[] hashBytes = MD5.HashData(inputBytes);

            return BitConverter.ToString(hashBytes).Replace("-", "");
        }

        public static Func<string, string> GetMD5(string salt = "") => source => {
            byte[] inputBytes = Encoding.ASCII.GetBytes(source + salt ?? "");
            byte[] hashBytes = MD5.HashData(inputBytes);

            return BitConverter.ToString(hashBytes).Replace("-", "");
        };
    }
}
