using PE.Domain.DoctorMng.Entities;
using PE.Domain.QuotationMng.Entities;
using PE.WebApi.Dtos.QuotationMng;
using PE.WebApi.Tests.Fakers.QuotationMng;
using PE.WebApi.Tests.Utils;
using Humanizer;

namespace PE.WebApi.Tests.QuotationMng.QuotationController;

[Collection(nameof(DatabaseCollection))]
public class Get_QuotationControllerTests(CustomWebApplicationFactory factory) : IAsyncLifetime
{
    private readonly CustomWebApplicationFactory _factory = factory;
    private readonly CreateQuotationDtoFaker _faker = new();
    private Doctor? _stubDoctor;
    private QuotationDto? _stubQuotation;

    [Fact]
    public async Task Get_返回报价单详情_当报价单存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var url = Urls.Quotation.Get.FormatWith(_stubQuotation!.Id);

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        var result = await response.Content.ReadFromJsonAsync<QuotationDto>();
        result.Should().NotBeNull();
        result!.Id.Should().Be(_stubQuotation.Id);
        result.QuotationNo.Should().Be(_stubQuotation.QuotationNo);
        result.CompanyId.Should().Be(_stubQuotation.CompanyId);
        result.Packages.Should().NotBeNull();
        result.Packages!.Count.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task Get_返回404_当报价单不存在时()
    {
        // Arrange
        var client = _factory.CreateClient();
        var nonExistentId = 99999;
        var url = Urls.Quotation.Get.FormatWith(nonExistentId);

        // Act
        var response = await client.GetAsync(url);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    public async Task InitializeAsync()
    {
        // 清理测试数据
        await CleanTestDataAsync();

        var client = _factory.CreateClient();

        // 创建测试用的医生数据
        var doctorDto = new CreateDoctorRequestFaker().Generate();
        var doctorResponse = await client.PostAsJsonAsync(Urls.Doctor.Create, doctorDto);
        doctorResponse.EnsureSuccessStatusCode();
        _stubDoctor = await doctorResponse.Content.ReadFromJsonAsync<Doctor>();

        // 创建测试用的报价单数据
        var quotationDto = _faker.Generate();
        var quotationUrl = $"{Urls.Quotation.Create}?salesmanId={_stubDoctor!.Id}";
        var quotationResponse = await client.PostAsJsonAsync(quotationUrl, quotationDto);
        quotationResponse.EnsureSuccessStatusCode();
        _stubQuotation = await quotationResponse.Content.ReadFromJsonAsync<QuotationDto>();
    }

    public async Task DisposeAsync()
    {
        // 清理测试数据
        await CleanTestDataAsync();
    }

    private static async Task CleanTestDataAsync()
    {
        // 清理报价单相关数据
        await TestDataCleaner.CleanAsync<ExaminationCombinedItem>();
        await TestDataCleaner.CleanAsync<ExaminationPackage>();
        await TestDataCleaner.CleanAsync<Quotation>();

        // 清理医生数据
        await TestDataCleaner.CleanAsync<Doctor>();
    }
}
