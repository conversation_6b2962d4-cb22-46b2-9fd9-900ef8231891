﻿using PE.Domain.DoctorMng.Entities;
using PE.Domain.DoctorMng.Repositories;
using PE.Domain.ManagementRecordMng.Contracts;
using PE.Domain.ManagementRecordMng.Entities;
using PE.Domain.ManagementRecordMng.Repositories;
using PE.Domain.ManagementRecordMng.Roles;
using PE.WebApi.Dtos;
using PE.WebApi.Dtos.ManagementRecordMng;

namespace PE.WebApi.Controllers.ManagementRecordMng;

public class ManagementRecordController(IMapper mapper,
    IDoctorRepository doctorRepository,
    IManagementRecordRepository managementRecordRepository)
    : BaseController
{
    private readonly IMapper _mapper = mapper;
    private readonly IDoctorRepository _doctorRepository = doctorRepository;
    private readonly IManagementRecordRepository _recordRepository = managementRecordRepository;


    /// <summary>
    /// 新建健康管理记录
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("create")]
    public async Task<ActionResult<ManagementRecordDto>> Create([FromBody] CreateManagementRecordDto dto)
    {
        ManagementRecord record = _mapper.Map<ManagementRecord>(dto);

        Doctor dr = await _doctorRepository.GetSingleAsync(d => d.Id == dto.AssistantId);
        Assistant assistant = _doctorRepository.InManagementContext().AsAssistant(dr);

        await assistant.CreateManagementRecordAsync(record);

        return Ok(_mapper.Map<ManagementRecordDto>(record));
    }


    /// <summary>
    /// 查询管理记录列表
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [HttpGet("page")]
    public async Task<ActionResult<PageResultDto<ManagementRecordDto>>> GetPageAsync([FromQuery] RecordsQuery query)
    {
        var (list, count) = await _recordRepository.GetPageAsync(new RecordPageQuery()
        {
            PageIndex = query.PageIndex,
            PageSize = query.PageSize,
            CustomerId = query.CustomerId,
            AssistantId = query.AssistantId,
            ExpertId = query.ExpertId,
            Status = query.Status
        });

        var items = _mapper.Map<List<ManagementRecordDto>>(list);

        return Ok(new PageResultDto<ManagementRecordDto>(query.PageIndex, query.PageSize, count, items));
    }


    //TODO: 添加阶段提交接口
}
