﻿using PE.Domain.QuotationMng.Relations;

namespace PE.Domain.QuotationMng.Entities;

/// <summary>
/// 团检套餐
/// </summary>
[SugarTable("group_examination_packages", TableDescription = "团检套餐")]
public class ExaminationPackage : Entity
{
    /// <summary>
    /// 套餐ID
    /// </summary>
    [SugarColumn(ColumnName = "id", IsPrimaryKey = true)]
    public string Id { get; set; }

    /// <summary>
    /// 报价单ID
    /// </summary>
    [SugarColumn(ColumnName = "quotation_id", ColumnDescription = "团检报价单ID")]
    public long QuotationId { get; set; }

    /// <summary>
    /// 套餐名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "套餐名称")]
    public string Name { get; set; }

    /// <summary>
    /// 套餐编码(检中)
    /// </summary>
    [SugarColumn(ColumnName = "package_code", ColumnDescription = "套餐编码")]
    public string PackageCode { get; set; }

    /// <summary>
    /// 套餐原价
    /// </summary>
    [SugarColumn(ColumnName = "original_price", ColumnDescription = "套餐原价")]
    public decimal OriginalPrice { get; set; }

    /// <summary>
    /// 折扣率
    /// </summary>
    [SugarColumn(ColumnName = "discount_rate", ColumnDescription = "折扣率")]
    public decimal DiscountRate { get; set; }

    /// <summary>
    /// 折后价
    /// </summary>
    [SugarColumn(ColumnName = "discounted_price", ColumnDescription = "折后价")]
    public decimal DiscountedPrice { get; set; }

    /// <summary>
    /// 套餐说明
    /// </summary>
    [SugarColumn(ColumnName = "description", Length = 500, ColumnDescription = "套餐说明")]
    public string Description { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(ColumnName = "is_active", ColumnDescription = "是否启用")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnName = "create_time", ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 适用性别限制
    /// </summary>
    [SugarColumn(ColumnName = "gender_limit", ColumnDescription = "适用性别限制")]
    public GenderType GenderLimit { get; set; }

    /// <summary>
    /// 最小年龄限制（0表示不限制）
    /// </summary>
    [SugarColumn(ColumnName = "min_age", ColumnDescription = "最小年龄限制（0表示不限制）")]
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄限制（0表示不限制）
    /// </summary>
    [SugarColumn(ColumnName = "max_age", ColumnDescription = "最大年龄限制（0表示不限制）")]
    public int MaxAge { get; set; }

    /// <summary>
    /// 适用婚姻状况
    /// </summary>
    [SugarColumn(ColumnName = "marital_status", ColumnDescription = "适用婚姻状况")]
    public MaritalStatus MaritalStatus { get; set; }


    /// <summary>
    /// 组合项明细
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public ICombinedItemOfPackage CombinedItems { get; set; }


    public override object GetIdentity()
    {
        return Id;
    }
}



/// <summary>
/// 性别类型枚举
/// </summary>
public enum GenderType
{
    /// <summary>
    /// 通用
    /// </summary>
    All = 0,

    /// <summary>
    /// 男
    /// </summary>
    Male = 1,

    /// <summary>
    /// 女
    /// </summary>
    Female = 2
}

/// <summary>
/// 婚姻状况枚举
/// </summary>
public enum MaritalStatus
{
    /// <summary>
    /// 通用
    /// </summary>
    All = 0,

    /// <summary>
    /// 未婚
    /// </summary>
    Unmarried = 1,

    /// <summary>
    /// 已婚
    /// </summary>
    Married = 2
}